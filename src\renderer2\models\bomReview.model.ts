import * as yup from 'yup';

export const bomReviewSchema = yup.object().shape({
    cart_items: yup.array().of(
        yup.object().shape({
            line_id: yup.number(),
            product_id: yup.number(),
            reference_product_id: yup.number(),
            buyer_pricing_lb: yup.string(),
            description: yup.string().required(),
            domestic_material_only: yup.boolean().oneOf([true, false, undefined]).default(null).nullable(),
            extended: yup.number(),
            price: yup.number(),
            price_unit: yup.string().required(),
            product_tag: yup.string().default(''),
            qty: yup.string().required(),
            qty_unit: yup.string().required(),
            seller_calculation_price: yup.number(),
            seller_extended: yup.number(),
            seller_price: yup.number(),
            shape: yup.string(),
            buyer_calculation_price: yup.number(),
            descriptionObj: yup.object().required(),
            sessionId: yup.string().default('').defined(),
            qty_um: yup.array(),
            price_um: yup.array(),
            line_session_id: yup.string(),
            extendedValue: yup.number()
        })
    ),
    price: yup.string(),
    seller_price: yup.string(),
    sales_tax: yup.number(),
    totalPurchase: yup.number(),
    depositAmount: yup.number(),
    payment_method: yup.string().required(),
    freight_term: yup.string()
});
