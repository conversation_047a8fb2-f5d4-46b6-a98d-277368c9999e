import { create } from 'zustand';

const defaultStore   = {
    viewIndex: 0,
    scrollPosition: 0,
}

interface BomReviewStore {
    viewIndex: number,
    scrollPosition: number,
    setViewIndex: (viewIndex: number) => void,
    setScrollPosition: (scrollPosition: number) => void,
}

export const useBomReviewStore = create<BomReviewStore>((set, get) => ({
    ...defaultStore,
    setViewIndex: (viewIndex: number) => set({ viewIndex }),
    setScrollPosition: (scrollPosition: number) => set({ scrollPosition }),
    resetBomReviewStore: () => set(defaultStore),
}));