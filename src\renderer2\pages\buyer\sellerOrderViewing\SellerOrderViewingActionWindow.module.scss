.actionWindow {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 16px;
  max-width: 400px;
  width: 100%;
  background-color: #191a20;
}

.summaryCard {
  font-family: Inter;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: left;
  color: #fff;
  display: flex;
  flex-direction: column;
}

.summaryRow {
  display: flex;
  justify-content: space-between;
}
.summaryRowSalesTax {
  opacity: 0.4;
}
.summaryCardContent {
  box-shadow: inset -2.1px -2px 4.1px 0 #000;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background-color: rgba(217, 217, 217, 0.04);
    height: 62px;
    padding: 10px;
    display: flex;
    flex-flow: column;
    gap: 6px;
}
.summaryTotal {
  display: flex;
  justify-content: space-between;
  background-image: linear-gradient(354deg, #000 142%, #191a20 4%);
  padding: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.infoText {
  font-family: Inter;
  font-size: 12px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.6;
  letter-spacing: -0.36px;
  text-align: center;
  color: #9b9eac;
  padding: 0 24px;
}

.acceptButtonDummy {
  border-radius: 10px;
  background-color: #222329;
  font-family: Syncopate;
  font-size: 18px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: -0.72px;
  text-align: center;
  color: #71737f;
  padding: 14px 0px;
  cursor: not-allowed;
}
.acceptButton {
  border-radius: 10px;
  background-color: #222329;
  font-family: Syncopate;
  font-size: 18px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: -0.72px;
  text-align: center;
  color: #71737f;
  padding: 14px 0px;
}
.availableToClaim {
    font-family: 'Syncopate', sans-serif;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: 0.48px;
    text-align: center;
    color: #32ccff;
}