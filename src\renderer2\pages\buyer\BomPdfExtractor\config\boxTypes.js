/**
 * Configuration for box types used in the PDF text extractor
 * This is the single source of truth for all box type information
 */
import { useBomPdfExtractorStore } from '../BomPdfExtractorStore';
const BOX_TYPES = {
  description: {
    id: 'description',
    label: 'DESC',
    abbreviation: 'DESC',
    selectedBg:'#3a86ff',
    hoverBg:'#9cc3ff',
    bgColor:'rgba(255, 255, 255, 0.14)',
    color: '#3a86ff',  // Red
    columnDef:{ field: "description", headerName: "Description", editable:true },
    fillColor: 'rgba(58, 134, 255, 0.2)',
    textColor: 'rgba(255, 255, 255, 1)',
    defaultTextColor: '#c2c2c2',
    hoverTextColor: '#0f0f14',
    minWidth: '70px',
    regex:[{
      type:'replace',
      regex:/ I /g,
      substiture: ' X '
    }]
  },
   length: {
    id: 'length',
    label: 'Length',
    abbreviation: 'LEN',
    selectedBg:'#0a9396',
    hoverBg:'#84c9cb',
    bgColor:'rgba(255, 255, 255, 0.14)',
    color: '#0a9396',  
    columnDef:{ field: "length", headerName: "Length", editable:true },
    fillColor: 'rgba(10, 147, 150, 0.2)',
    textColor: 'rgba(255, 255, 255, 1)',
    defaultTextColor: '#c2c2c2',
    hoverTextColor: '#0f0f14',
    minWidth: '90px',
  },

 lengthum: {
    id: 'lengthum',
    label: 'LENGTH UM',
    abbreviation: 'L UM',
    selectedBg:'#94d2bd',
    hoverBg:'#c9e9de',
    bgColor:'rgba(255, 255, 255, 0.14)',
    color: '#94d2bd',  
    columnDef:{ field: "lengthUM", headerName: "Length UM", editable:true },
    fillColor: 'rgba(148, 210, 189, 0.2)',
    textColor: 'rgba(0, 0, 0, 1)',
    defaultTextColor: '#c2c2c2',
    hoverTextColor: '#0f0f14',  
    minWidth: '117px',
  },

 partNumber: {
    id: 'partNumber',
    label: 'PART#',
    abbreviation: 'PART',
    selectedBg:'#3947b4',
    hoverBg:'#7686ff',
    bgColor:'rgba(255, 255, 255, 0.14)',
    color: '#3947b4',  
    columnDef:{ field: "partNumber", headerName: "Part Number", editable:true },
    fillColor: 'rgba(148, 168, 210, 0.4)',
    textColor: 'rgba(255, 255, 255, 1)',
    defaultTextColor: '#c2c2c2',
    hoverTextColor: '#0f0f14',
    minWidth: '82px',
  },

quantity: {
  id: 'quantity',
  label: 'QTY',
  abbreviation: 'QTY',
  selectedBg:'#ff006e',
  hoverBg:'#ff80b6',
  bgColor:'rgba(255, 255, 255, 0.14)',
  color: '#ff006e',  // Green
  fillColor: 'rgba(255, 0, 110, 0.2)',
  textColor: 'rgba(255, 255, 255, 1)',
  defaultTextColor: '#c2c2c2',
  hoverTextColor: '#0f0f14',
  minWidth: '70px',
  columnDef:{ field: "quantity", headerName: "Quantity", editable:true },
  regex:[{
    type:'extract',
    regex:/^([^ ]+)/g,
  }]
},
  quantityum: {
    id: 'quantityum',
    label: 'QTY UM',
    abbreviation: 'Q UM',
    selectedBg:'#8338ec',
    hoverBg:'#c19bf5',
    bgColor:'rgba(255, 255, 255, 0.14)',
    color: '#8338ec',  
    columnDef:{ field: "quantityum", headerName: "QTY UM", editable:true },
    fillColor: 'rgba(131, 56, 236, 0.2)',
    textColor: 'rgba(255, 255, 255, 1)',
    defaultTextColor: '#c2c2c2',
    hoverTextColor: '#0f0f14',
    minWidth: '90px',
  },

  specification: {
    id: 'specification',
    label: 'SPEC',
    abbreviation: 'SPEC',
    selectedBg:'#fb5607',
    hoverBg:'#fdaa83',
    bgColor:'rgba(255, 255, 255, 0.14)',
    color: '#fb5607',  // Blue
    columnDef:{ field: "specification", headerName: "Specification", editable:true },
    fillColor: 'rgba(251, 86, 7, 0.2)',
    textColor: 'rgba(255, 255, 255, 1)',
    defaultTextColor: '#c2c2c2',
    hoverTextColor: '#0f0f14',
    minWidth: '72px',
    },

  grade: {
    id: 'grade',
    label: 'GRADE',
    abbreviation: 'GR',
    selectedBg:'#ffbe0b',
    hoverBg:'#ffdf85',
    bgColor:'rgba(255, 255, 255, 0.14)',
    color: '#ffbe0b',  // Yellow
    columnDef:{ field: "grade", headerName: "Grade", editable:true },
    fillColor: 'rgba(255, 190, 11, 0.2)',
    textColor: 'rgba(0, 0, 0, 1)',
    defaultTextColor: '#c2c2c2',
    hoverTextColor: '#0f0f14',
    minWidth: '88px',
  },

  // weight: {
  //   id: 'weight',
  //   label: 'Weight',
  //   abbreviation: 'WT',
  //   selectedBg:'#8338ec',
  //   hoverBg:'#ba91f4',
  //   bgColor:'rgba(255, 255, 255, 0.14)',
  //   color: '#8338ec',  
  //   fillColor: 'rgba(131, 56, 236, 0.2)'
  // },
  // weight_per_quantity: {
  //   id: 'weight_per_quantity',
  //   label: 'Weight per Quantity',
  //   abbreviation: 'DESC',
  //   color: '#800080',  // Purple
  //   fillColor: 'rgba(128,0,128,0.1)'
  // },

};

// Legacy box types for backward compatibility
const LEGACY_BOX_TYPES = {
  Description: 'description',
  Length: 'length',
  'Spec-Grade': 'specification',
  Qty: 'quantity'
};

// Get an array of box type IDs
const getBoxTypeIds = () => {
  const { customBoxTypes } = useBomPdfExtractorStore();
  return [...Object.keys(BOX_TYPES), ...customBoxTypes];
};

// Get an array of box type labels
const getBoxTypeLabels = () => {
  const { customBoxTypes } = useBomPdfExtractorStore();
  return [...Object.values(BOX_TYPES).map(type => type.label), ...customBoxTypes.map(type => type.label)];
};

// Get box styles for drawing
const getBoxStyles = () => {
  const styles = {};
  const { customBoxTypes } = useBomPdfExtractorStore();
  // Add new box types
  Object.entries(BOX_TYPES).forEach(([id, type]) => {
    styles[id] = { 
      stroke: type.color, 
      fill: type.fillColor,
      abbreviation: type.abbreviation,
      textColor: type.textColor
    };
  });

  // Add custom box types
  if (customBoxTypes && Array.isArray(customBoxTypes)) {
    customBoxTypes.forEach((customType) => {
      styles[customType.id] = {
        stroke: customType.color || customType.selectedBg || '#3a86ff',
        fill: customType.fillColor || 'rgba(58, 134, 255, 0.2)',
        abbreviation: customType.abbreviation || customType.label || 'CUSTOM',
        textColor: customType.textColor || 'rgba(255, 255, 255, 1)'
      };
    });
  }
  
  // Add legacy box types for backward compatibility
  Object.entries(LEGACY_BOX_TYPES).forEach(([legacyId, newId]) => {
    styles[legacyId] = { 
      stroke: BOX_TYPES[newId].color, 
      fill: BOX_TYPES[newId].fillColor 
    };
  });
  
  return styles;
};

// Create empty data arrays for all box types
const createEmptyDataArrays = (length = 0) => {
  const data = {};
  
  // Create arrays for new box types
  Object.keys(BOX_TYPES).forEach(id => {
    data[`${id}Arr`] = Array(length).fill('');
  });
  
  // Create arrays for legacy box types
  data.specGradeArr = Array(length).fill('');
  data.qtyArr = Array(length).fill('');
  
  return data;
};

// Get CSV headers
const getCsvHeaders = () => {
  return ['Page', ...Object.values(BOX_TYPES).map(type => type.label)].join(',');
};

// Map a legacy box type to its new equivalent
const mapLegacyBoxType = (legacyType) => {
  return LEGACY_BOX_TYPES[legacyType] || legacyType;
};

export {
  BOX_TYPES,
  LEGACY_BOX_TYPES,
  getBoxTypeIds,
  getBoxTypeLabels,
  getBoxStyles,
  createEmptyDataArrays,
  getCsvHeaders,
  mapLegacyBoxType
};
