import clsx from 'clsx';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import BomTile from '../bomTile/BomTile';
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import { creditLimitStatus, localStorageKeys, routes } from 'src/renderer2/common';
import { useBomReviewStore } from '../BomReview/BomReviewStore';
import { useGenericForm } from 'src/renderer2/hooks/useGenericForm';
import { bomReviewSchema } from 'src/renderer2/models/bomReview.model';
import { useFieldArray } from 'react-hook-form';
import { getFloatRemainder, getValUsingUnitKey, newPricingPrefix, orderIncrementPrefix, priceUnits, uploadBomConst, useBuyerSettingStore, useCreatePoStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { useLocation } from 'react-router-dom';
import { calculateBuyerTotalOrderWeightForGear } from 'src/renderer2/utility/pdfUtils';
import useGetAvailableCreditLimit from 'src/renderer2/hooks/useGetAvailableCreditLimit';
import { useBomPdfExtractorStore } from '../BomPdfExtractor/BomPdfExtractorStore';
interface FormErrorData {
    qty?: boolean;
    product?: boolean;
    qtyEmpty?: boolean;
}

interface FormErrors {
    [key: number]: FormErrorData;
}

const BomReviewTable = forwardRef<any, any>((
    {
        styles,
        createPoContainerRef,
        formInputGroupRef,
        hidePoLineScroll,
        setHidePoLineScroll,
        addPoLineTableRef,
        bomUploadResult,
        products,
        userPartData,
        sessionId,
        searchStringData,
        setSearchString,
        setLineSessionId,
        lineSessionId,
        setDisableBidBuyNow,
        setOpenDeliveryToDialog,
        scrollToTop,
        currentBomData,
        scrollPoHeaderToBottom,
        setFocusJobPoInput,
        scrollerRef,
        setMaxScrollHeight,
        setCurrentBomData,
        isHeaderDetailsConfirmed,
        initializePoHeaderForm,
        isSavedBom,
        setOpenErrorDialog,
        setErrorMessage,
        setCameFromSavedBom,
        maxScrollHeight,
        isProgrammaticScroll,
        setIsProgrammaticScroll
    }, ref
) => {
    const location = useLocation();
    const setShowLoader = useGlobalStore((state: any) => state.setShowLoader);
    const productMapping = useGlobalStore((state: any) => state.productMapping);
    const bomSummaryViewFilter = useCreatePoStore((state: any) => state.bomSummaryViewFilter);
    const setBomSummaryViewFilter = useCreatePoStore((state: any) => state.setBomSummaryViewFilter);
    const bomProductMappingDataFromSavedBom = useCreatePoStore((state: any) => state.bomProductMappingDataFromSavedBom);
    const createPoDataFromSavedBom = useCreatePoStore((state: any) => state.createPoDataFromSavedBom);
    const createPoData = useCreatePoStore((state: any) => state.createPoData);
    const uploadBomInitialData = useCreatePoStore((state: any) => state.uploadBomInitialData);
    const setBomProductMappingSocketData = useCreatePoStore((state: any) => state.setBomProductMappingSocketData);
    const buyerSetting = useBuyerSettingStore((state: any) => state.buyerSetting);
    const setCreatePoData = useCreatePoStore((state: any) => state.setCreatePoData);
    const setUploadBomInitialData = useCreatePoStore((state: any) => state.setUploadBomInitialData);
    const  setProps = useRightWindowStore((state: any) => state.setProps); 
    const  props = useRightWindowStore((state: any) => state.props); 
    const orderInfoIsFilled = useCreatePoStore((state: any) => state.orderInfoIsFilled);
    const getAvailableCreditLimit = useGetAvailableCreditLimit();

    const {setScrollToBomLine, scrollToBomLine} = useRightWindowStore();
    const lastModifiedBomRef = useRef(null);
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const setViewIndex = useBomReviewStore((state) => state.setViewIndex);
    // const [viewIndex, setViewIndex] = useState(0);
    const viewIndex = useBomReviewStore((state) => state.viewIndex);
    const [pageIndex, setPageIndex] = useState(0);
    const [filteredFields, setFilteredFields] = useState<any>([]);
    const [initialData, setInitialData] = useState(location.pathname !== routes.savedBom ? createPoData : null);
    // Flag to prevent infinite loops when programmatically setting scrollTop
    const startIndex = pageIndex*20;
    const endIndex = (pageIndex+1)*20;
    let viewLineStatusIndex = 0;
    const [formErrors, setFormErrors] = useState<FormErrors>({});
    const lineItemsToLoad = 20;
    const [bomUploadResultCopy, setBomUploadResultCopy] = useState<any>([]);
    const [pageCount, setPageCount] = useState(0);
    const [disableReviewCompleteButton, setDisableReviewCompleteButton] = useState(true);
    const [didScrollToPreviousSavedBomLine, setDidScrollToPreviousSavedBomLine] = useState(false);
    const {setPdfUrl, setBomUploadID, setPdfFileName} = useBomPdfExtractorStore();

    const {
        control,
        register,
        getValues,
        setValue,
        setError,
        watch,
        clearErrors,
        reset,
        trigger,
        errors
    } = useGenericForm(bomReviewSchema, {
        defaultValues: {
            'cart_items':  [],
            'freight_term': "Delivered"
        }
    });

    
    const { fields, append } = useFieldArray({
        control,
        name: "cart_items"
    });

    useEffect(()=>{
        if(createPoContainerRef.current){
            if(pageIndex > 0){
                createPoContainerRef.current.style.overflowY = 'hidden';
            }else{
                createPoContainerRef.current.style.overflowY = 'auto';
            }
        }
    },[pageIndex])

    useEffect(()=>{
        if(createPoContainerRef.current){
            if(!hidePoLineScroll){
                if(addPoLineTableRef.current?.scrollTop > 100){
                    createPoContainerRef.current.style.overflowY = 'hidden';
                }else if(pageIndex === 0){
                    createPoContainerRef.current.style.overflowY = 'auto';
                }
                scrollerRef.current?.updateScrollPosition(createPoContainerRef.current?.scrollTop + 234*5*pageIndex + addPoLineTableRef.current?.scrollTop);
            }else{
                createPoContainerRef.current.style.overflowY = 'auto';
            }
        }
    },[hidePoLineScroll])

    useEffect(()=>{
        if(currentBomData?.id && bomUploadResultCopy?.length > 0 && !didScrollToPreviousSavedBomLine){
            const _lastModifiedBom = getLastModifiedBom(currentBomData?.id);
            setDidScrollToPreviousSavedBomLine(true);
            setTimeout(() => {
                handleScrollToBomLine(_lastModifiedBom?.lineId, _lastModifiedBom?.input);
            }, 500);
        }
    },[currentBomData, bomUploadResultCopy])

    const checkAtLeastOneApproved = bomUploadResultCopy?.some((item: any) => item.lineStatus === 'APPROVED')
    const isInvalidBomForm = bomUploadResultCopy?.some((item: any, index: number) => {
        if (item.lineStatus === 'SKIPPED' || item.lineStatus === 'DELETED') {
            return false;
        } else if (item.lineStatus === 'APPROVED') {
            return formErrors[index]?.qty || formErrors[index]?.product || formErrors[index]?.qtyEmpty
        }
        return (
            item.lineStatus === 'PENDING'
        );
    })
    useEffect(()=>{
        setDisableReviewCompleteButton(!orderInfoIsFilled || !checkAtLeastOneApproved || isInvalidBomForm);
    },[orderInfoIsFilled, checkAtLeastOneApproved, isInvalidBomForm]);

    useEffect(()=>{
        setProps({...props, disableReviewCompleteButton, bomUploadResultCopy});        
    },[disableReviewCompleteButton, bomUploadResultCopy])

    const getInitialData = () => {
        if (location.pathname === routes.bomUploadReview && location.state?.from === 'bomPdfExtractor') {
            return uploadBomInitialData;
        }
        return initialData;
    }

    useEffect(() => {
        reset();
    }, []);

    // Use the tracked length in useEffect
    useEffect(() => {
        const formInputGroup = formInputGroupRef.current;
        // const addLineContainer = addPoLineTableRef.current;
        const tableHeight = 234 * bomUploadResult.length;
        if (!!formInputGroup) {
            setTimeout(() => {
                setMaxScrollHeight((formInputGroup?.scrollHeight + tableHeight));
            }, 200)
        }
    }, [bomUploadResult.length]);  // Now depends on the tracked length

    useEffect(() => {
        const initializeData = async () => {
            if (initialData && location.pathname === routes.savedBom) {
                await initializeCreatePOData();
            }
        }
        initializeData();
    }, [initialData, location.pathname])

    useEffect(() => {
        const initializeData = async () => {
            if (isHeaderDetailsConfirmed) {
                await initializeCreatePOData();
            }
        };

        initializeData();
    }, [isHeaderDetailsConfirmed])

    

    const initializeCreatePOData = async () => {
        try {
            const _intialData = getInitialData();
            
            if (_intialData) {
                setValue('id', _intialData?.id ?? '');
                if (_intialData?.cameFromSavedBom && !isHeaderDetailsConfirmed) {
                    handleHeaderDetailsFill()
                } else {
                    if (!isHeaderDetailsConfirmed) {
                        initializePoHeaderForm(_intialData);
                    }
                    setValue('sales_tax', _intialData?.sales_tax ?? 0);
                    if (isSavedBom) {
                        setValue('price', _intialData?.material_total)
                        setValue('totalPurchase', _intialData?.total_purchase)
                        setValue('depositAmount', _intialData?.deposit)
                        setValue('subscriptionAmount', _intialData?.subscription)
                        setValue('totalWeight', parseInt(_intialData?.total_weight) ?? 0);
                    } else {
                        setValue('totalWeight', calculateBuyerTotalOrderWeightForGear(watch('cart_items')));
                        // calculateMaterialTotalPrice();
                    }
                }
            } else {
                setValue('sales_tax', 0);
            }
        } catch (err) {
            setOpenErrorDialog(true);
            setErrorMessage("Something went wrong. Please try again in sometime");
            setShowLoader(false);
            console.error(err)

        } finally {
            setCreatePoData(null);
        }
    }

    const handleHeaderDetailsFill = () => {
        setCameFromSavedBom(true);
        initializePoHeaderForm(initialData);
    }
    const initializeBomTileData = (index: number) => { 
        const selectedProduct = watch(`cart_items.${index}.selected_products`);
        if (selectedProduct?.length > 0) {
          const product = productMapping[selectedProduct[0]];
          if(product){
            setValue(`cart_items.${index}.descriptionObj`, product);
            setValue(`cart_items.${index}.qty_um`, product.QUM_Dropdown_Options?.split(","));
            
            // Only set qty_unit if 'Ea' is not in QUM options
            const qumOptions = product.QUM_Dropdown_Options?.split(",");
            if (!qumOptions.includes(watch(`cart_items.${index}.qty_unit`))) {
                setValue(`cart_items.${index}.qty_unit`, qumOptions[0]);
            }
          }
        }
    }

    useEffect(() => {
        if (bomUploadResult?.length > 0) {
            setValue('cart_items', []);
            for (let i = 0; i < bomUploadResult.length; i++) {
                setDisableBidBuyNow(true);
                if(i <lineItemsToLoad){
                append({ ...bomUploadResult[i] })
                }
                initializeBomTileData(i)
                validateSavedBomCreatePo(bomUploadResult[i])
            }
            if(bomUploadResult.length > lineItemsToLoad){
                setPageCount(Math.ceil((bomUploadResult.length-lineItemsToLoad) / (lineItemsToLoad/4)));
            }else{
                setPageCount(0);
            }
            setBomUploadResultCopy(structuredClone(bomUploadResult))
        }
    }, [bomUploadResult])

    useEffect(()=>{
        if(bomUploadResultCopy?.length > 0){
            setShowLoader(false);
        }
    },[bomUploadResultCopy])

    useEffect(() => {
        if (bomUploadResultCopy?.length > 0) {
            handleFilterFieldsData(0, true);
            setPageIndex(0);
            setIsProgrammaticScroll(true);
            setTimeout(() => {
                if(createPoContainerRef.current?.scrollTop < 230 && addPoLineTableRef.current ){
                    addPoLineTableRef.current.style.overflowY = 'hidden';
                }
                addPoLineTableRef.current?.scrollTo({ top: 0 });
                setIsProgrammaticScroll(false);
            }, 200)

        }
    }, [bomSummaryViewFilter])

    const getFilteredFieldsData = (checkAtLeastOneValid: boolean = true) => {
        const atLeastOneValid = checkAtLeastOneValid ? bomUploadResultCopy.some((item: any, index: number) => {
            const lineStatus = item?.lineStatus;
            return (
                (bomSummaryViewFilter === 'red' && lineStatus === uploadBomConst.lineItemStatus.pending) ||
                (bomSummaryViewFilter === 'green' && lineStatus !== uploadBomConst.lineItemStatus.pending)
            );
        }) : true;
        const filteredFieldsData = bomUploadResultCopy.filter((item: any, index: number) => {
            const lineStatus = item?.lineStatus
            const checkValidation = ((bomSummaryViewFilter === 'all' || !atLeastOneValid) || (bomSummaryViewFilter === 'red' && lineStatus === uploadBomConst.lineItemStatus.pending) || (bomSummaryViewFilter === 'green' && lineStatus !== uploadBomConst.lineItemStatus.pending))
            return checkValidation;
        })
        setFilteredFields(filteredFieldsData);
        const formInputGroup = formInputGroupRef.current;
        const tableHeight = 234 * filteredFieldsData.length;
        setMaxScrollHeight((formInputGroup?.scrollHeight + tableHeight));
        return filteredFieldsData;
    }

    const handleFilterFieldsData = (pageIndex: number, adjustScroll: boolean = false) => {
        const filteredFieldsData = getFilteredFieldsData();
        const pageCount = filteredFieldsData.length <= lineItemsToLoad ? 0 : Math.ceil((filteredFieldsData.length-lineItemsToLoad) / (lineItemsToLoad/4));
        setPageCount(pageCount);
        setValue('cart_items', filteredFieldsData.slice(pageIndex*5, (pageIndex*5)+20));
        if(adjustScroll){
            if(scrollerRef.current) scrollerRef.current.updateScrollPosition(createPoContainerRef.current?.scrollTop);
        }
    }


    useEffect(() => {
        if (location.pathname === routes.savedBom && (createPoDataFromSavedBom || bomProductMappingDataFromSavedBom)) {
            reset();
            setViewIndex(0);
            setBomSummaryViewFilter('all')
            setInitialData(null);
            setBomProductMappingSocketData(null);
            setScrollToBomLine(null)
            if (bomProductMappingDataFromSavedBom) {
                setInitialData({ ...bomProductMappingDataFromSavedBom });
                setCurrentBomData({ ...bomProductMappingDataFromSavedBom });
                setPdfFileName(bomProductMappingDataFromSavedBom?.actual_file_name);    
                setPdfUrl(bomProductMappingDataFromSavedBom?.s3_url);
                setBomUploadID(bomProductMappingDataFromSavedBom?.id);
                let formattedUploadBomHeaderData = {
                    delivery_date: bomProductMappingDataFromSavedBom?.delivery_date ,
                    shipping_details: bomProductMappingDataFromSavedBom?.shipping_details,
                    order_type: bomProductMappingDataFromSavedBom?.type,
                    internal_po_number: bomProductMappingDataFromSavedBom?.title,
                    bom_id: bomProductMappingDataFromSavedBom?.id,
                }
                setUploadBomInitialData({...formattedUploadBomHeaderData})
            } else if (createPoDataFromSavedBom) {
                handleCreatePoDataFromSavedBom();
            }
        }
    }, [location.pathname, createPoDataFromSavedBom, bomProductMappingDataFromSavedBom])
    
    const handleCreatePoDataFromSavedBom = () => {
        const _createPoDataFromSavedBom = { ...createPoDataFromSavedBom };
        const cartItem = _createPoDataFromSavedBom?.result?.map((item, i) => ({
            bom_line_id: item.id || i,
            lineStatus: item.status,
            originalStatus: item.original_line_status || item.status,
            confidence: item.confidence,
            product_tag: item.product_tag,
            description: item.description,
            specification: item.specification,
            search_string: item.search_string,
            matched_products: item.matched_products,
            selected_products: item.selected_products,
            current_page: item.current_page,
            total_pages: item.total_pages,
            product_index: item.product_index,
            grade: item.grade,
            qty: item.qty,
            qty_unit: item.qty_unit,
            length: item.length,
            weight_per_quantity: item.weight_per_quantity,
            matched_product_count: item.matched_product_count,
            price_unit: item.price_unit,
            price: item.price_per_unit,
            line_weight: item.line_weight ?? '0.00',
            extended: item?.buyer_line_total ? parseFloat(parseFloat(item.buyer_line_total).toFixed(2)) : 0,
            domesticMaterialOnly: item?.domestic_material_only || false,
            product_id: item.product_id,
            draft_line_id: item.id
        }));

        if (cartItem) {
            cartItem.forEach((item: any, index: number) => {
                if (item?.product_id) {
                    const product = productMapping[item.product_id];
                    if (product) {
                        item.descriptionObj = product;
                    }
                } else if (item?.selected_products?.length) {
                    const selectedProducts = item.selected_products;
                    const hasSelectedProducts = selectedProducts.length > 0;
                    if (hasSelectedProducts) {
                        const product = productMapping[selectedProducts[0]];
                        // Directly set the values on the cartItem object
                        item.descriptionObj = product;
                    }
                }
            });
        }
        setValue('cart_items', cartItem);
        setInitialData({ ...createPoDataFromSavedBom, cart_items: cartItem });
    }

    const getLastModifiedBom = (key: string): string | null => {
        try {
            const lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
            if (lastModifiedBom) {
                const lastModifiedBomData = JSON.parse(lastModifiedBom);
                if (key in lastModifiedBomData) {
                    return lastModifiedBomData[key];
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } catch (e) {
            console.warn('Error checking key in localStorage', e);
            return null;
        }
    };

    const validateSavedBomCreatePo = useCallback((item: any) => {
        // const item = watch(`cart_items.${index}`);
        let descriptionObj = item?.descriptionObj;
        if (item?.selected_products?.length && !descriptionObj) {
            const selectedProducts = item.selected_products;
            const hasSelectedProducts = selectedProducts.length > 0;
            if (hasSelectedProducts) {
                const product = productMapping[selectedProducts[0]];
                // Directly set the values on the cartItem object
                descriptionObj = product;
            }
        }
        if (descriptionObj && Object.keys(descriptionObj).length > 0) {
            const _selected = descriptionObj;
            
            if (_selected) {
                const qtyVal = +item.qty || 0;
                const qtyUnit = item.qty_unit;
                const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options?.split(",")[0] : '')).toLowerCase();
                const updatedUnit = unit;
                const orderIncrement = getValUsingUnitKey(_selected, updatedUnit, orderIncrementPrefix);
                if (qtyVal && orderIncrement) {
                    // console.log("qtyVal", qtyVal, orderIncrement);
                    if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                        setFormErrors(prevErrors => {
                            const newErrors = {...prevErrors};
                            delete newErrors[item.lineStatusIndex];
                            return newErrors;
                        });

                        // clearErrors(`cart_items.${index}.qty`);
                        // trigger(`cart_items.${index}.qty`);
                        return true;
                    } else {
                        if (_selected && (item.lineStatus === uploadBomConst.lineItemStatus.approved || item.lineStatus == uploadBomConst.lineItemStatus.pending)) {
                            setFormErrors(prevErrors => ({
                                ...prevErrors,
                                [item.lineStatusIndex]: {
                                    qty: true,
                                    product: false,
                                    qtyEmpty: false
                                }
                            }));
                        }
                        // if (_selected) setError(`cart_items.${index}.qty`, { message: `Quantity can only be multiples of ${orderIncrement}` }, { shouldFocus: false })
                        
                        // setValue(`cart_items.${index}.extended`, 0);
                        // setValue(`cart_items.${index}.seller_extended`, 0);
                    }
                }
                else {
                    setFormErrors(prevErrors => ({
                        ...prevErrors,
                        [item.lineStatusIndex]: {
                            qty: false,
                            product: false,
                            qtyEmpty: true
                        }
                    }));
                }
            }
        }else{
            setFormErrors(prevErrors => ({
                ...prevErrors,
                [item.lineStatusIndex]: {
                    qty: false,
                    product: true,
                    qtyEmpty: false
                }
            }));
        }
    }, [watch, getValues, productMapping, setValue]);

    const focusChildElement = (parentElement: HTMLElement | any, elementToFocus?: string | null) => {
        if(!elementToFocus) return;
        if (elementToFocus === "qty") {
            const childElement = parentElement.querySelector('[id^="qty-input-"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else if (elementToFocus === "product_tag") {
            const childElement = parentElement.querySelector('[name^="cart_items."][name$=".product_tag"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else {
            const childElement = parentElement.querySelector('[id^="combo-box-demo"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        };
    }

    const handleLineStatusChange = () => {
        if(bomSummaryViewFilter !== 'all'){
            const filteredFieldsData = getFilteredFieldsData(false);
            const list = filteredFieldsData.slice(pageIndex*5, pageIndex*5+20)
            if(list.length === 0 && pageIndex === 0){
                setBomSummaryViewFilter('all');
            }else{
                setValue(`cart_items`, filteredFieldsData.slice(pageIndex*5, pageIndex*5+20));
            }
        }
    }

    const scrollBomLineToSpecificIndex = (bomLineId: string, elementToFocus?: string | null) => {
        const scrollToElement = (table: HTMLElement, element: HTMLElement) => {
            const elementOffsetTop = element.offsetTop;
            setIsProgrammaticScroll(true);
            table.scrollTo({ top: elementOffsetTop - 50 });
            setTimeout(() => {
                setIsProgrammaticScroll(false);
            }, 200)
        };
        const table = addPoLineTableRef.current;
        const element = document.getElementById(bomLineId);

        if (table && element) {
            scrollToElement(table, element);
            focusChildElement(element, elementToFocus);
        }
        setScrollToBomLine(null)
    }

    useEffect(() => {
        if (scrollToBomLine) {
            handleScrollToBomLine(scrollToBomLine, "description")
        }
    }, [scrollToBomLine])
    // console.log("filteredFields", filteredFields);

    const handleScrollToBomLine = (bomLineId: string, elementToFocus?: string|null, isDragging: boolean=false) => {
        const bomList = filteredFields.length > 0 ? filteredFields : bomUploadResultCopy;
        const index = bomList.findIndex((item: any) => item.bom_line_id === bomLineId);
        if (index !== -1) {
            const _pageIndex = (index+1) - 15 <=0 ? 0 : Math.ceil(((index+1)-15)/5)+1;
            setPageIndex(_pageIndex);
            const nextList = [...bomList.slice(_pageIndex*5, (_pageIndex*5)+20)];
            setValue('cart_items', nextList);
            setIsProgrammaticScroll(true);
            if(!isDragging) scrollPoHeaderToBottom()
            setTimeout(() => {
                if(!isDragging && createPoContainerRef.current){
                    const element = document.getElementById(bomLineId);
                    const position = createPoContainerRef.current.scrollTop + _pageIndex*234*5 + element?.offsetTop - 200;
                    if(scrollerRef.current) scrollerRef.current.updateScrollPosition(position);
                }
                scrollBomLineToSpecificIndex(bomLineId, elementToFocus);
                setTimeout(() => {
                    setIsProgrammaticScroll(false);
                }, 200)
            }, 500)
        }
    }

    const handleLineItemScroll = () => {
        // Skip if the scroll is programmatic
        if (!addPoLineTableRef.current || isProgrammaticScroll) return;
        const { scrollTop, scrollHeight, clientHeight } = addPoLineTableRef.current;
        const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 5; // 5px threshold
        if(isAtBottom && pageIndex < pageCount){
            const nextIndex = (pageIndex + 1);
            handleFilterFieldsData(nextIndex);
            setPageIndex((prev) => prev + 1);
            setIsProgrammaticScroll(true);
            setTimeout(()=>{
                if(addPoLineTableRef.current) addPoLineTableRef.current.scrollTop = scrollTop - 234*5;
                handleViewIndexChange();
            }, 100)
        }
        else if(pageIndex > 0 && scrollTop < 234){
            const nextIndex = (pageIndex - 1);
            handleFilterFieldsData(nextIndex);
            setPageIndex((prev) => prev - 1);
            // Set flag before programmatic scroll
            setIsProgrammaticScroll(true);
            setTimeout(()=>{
                addPoLineTableRef.current.scrollTop = scrollTop + 234*5;
                handleViewIndexChange();
            }, 100)
        }else{
            handleViewIndexChange();
        }  
        if(scrollerRef.current) scrollerRef.current.updateScrollPosition(createPoContainerRef.current.scrollTop + pageIndex*234*5 + scrollTop);
        // Reset flag after a short delay
        setTimeout(() => {
            setIsProgrammaticScroll(false);
        }, 200);
    }
    
    const handleViewIndexChange = () => {
        const container = addPoLineTableRef.current;
        if (container) {
            const firstRow = container.querySelector('tbody tr:first-child');
            if (firstRow) {
                const { scrollTop } = container;
                const rect = firstRow.getBoundingClientRect();

                // Get computed styles to access margins
                const computedStyle = window.getComputedStyle(firstRow);

                // Calculate total height including margins
                const marginTop = parseInt(computedStyle.marginTop, 10);
                const marginBottom = parseInt(computedStyle.marginBottom, 10);

                // Total height = height + margin-top + margin-bottom
                const firstRowHeight = rect.height + marginTop + marginBottom;
                const index = Math.floor(scrollTop / firstRowHeight);
                
                // // Clear existing timeout
                // if (debounceTimeoutRef.current) {
                //     clearTimeout(debounceTimeoutRef.current);
                // }

                // // Set new timeout to debounce the setViewIndex call
                // debounceTimeoutRef.current = setTimeout(() => {
                // }, 400);
                setViewIndex(pageIndex*5+index);
                
                
            } else {
                console.warn('First row not found');
            }
        }
    };

    const openAddLineTab = () => {
        if (!addPoLineTableRef.current || !hidePoLineScroll) return;
        const container = addPoLineTableRef.current;
        if (container.scrollTop > 0 && orderInfoIsFilled && createPoContainerRef.current) {
            createPoContainerRef.current.scrollTo({
                top: createPoContainerRef.current.scrollHeight,
                behavior: 'smooth'
            });
            container.style.overflowY = 'auto';
            const formInputHeight = formInputGroupRef.current?.clientHeight;
            setTimeout(() => {
               if(scrollerRef.current) scrollerRef.current.updateScrollPosition(pageIndex*234*5 + container.scrollTop + formInputHeight);
            }, 400)
        }
    }

    const saveModifiedBom = (index: number, input: any) => {
        try {
            if(currentBomData?.id && watch(`cart_items.${index}`)?.bom_line_id){
            const data ={
                [currentBomData?.id]: {lineId: watch(`cart_items.${index}`)?.bom_line_id, input: input, pageIndex}
            }
            const _lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
            if(_lastModifiedBom){
                const _lastModifiedBomData = JSON.parse(_lastModifiedBom);
                _lastModifiedBomData[currentBomData?.id] = {lineId: watch(`cart_items.${index}`)?.bom_line_id, input: input, pageIndex};
                localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(_lastModifiedBomData));
            }else{
                localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(data));
            }
            }
        } catch (e) {
            console.warn('Could not store value in localStorage', e);
        }
    }

    const handleScrollerDrag = (newScrollPosition: number) => {
        setIsProgrammaticScroll(true);
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
        }
        if(newScrollPosition >= 0 && newScrollPosition < 204){
            debounceTimeoutRef.current = setTimeout(() => {
                createPoContainerRef.current.scrollTop = newScrollPosition;
                handleScrollToBomLine(bomUploadResultCopy[0]?.bom_line_id, null,true);
                setIsProgrammaticScroll(false);
            }, 200)
        }
        else if(scrollerRef.current?.isAtBottom(100)){
            // createPoContainerRef.current.scrollTop = newScrollPosition;
            // Set new timeout to debounce the setViewIndex call
            debounceTimeoutRef.current = setTimeout(() => {
                createPoContainerRef.current.scrollTop = newScrollPosition;
                handleScrollToBomLine(bomUploadResultCopy[bomUploadResultCopy.length-1]?.bom_line_id, null,true);
            }, 200);
        }
        else if(newScrollPosition >= 204){
            
            // Set new timeout to debounce the setViewIndex call
            // console.log("newScrollPosition", newScrollPosition, maxScrollHeight);
            debounceTimeoutRef.current = setTimeout(() => {
                createPoContainerRef.current.scrollTop = newScrollPosition;
                const index = Math.floor((newScrollPosition - 204)/234);
                const bomLineId = bomUploadResultCopy[index]?.bom_line_id;
                handleScrollToBomLine(bomLineId, null,true);
            }, 200);
        }
        
        // if(newScrollPosition >= 0 && newScrollPosition < 204){
        //     headerScroll = newScrollPosition;
        //     tableScroll = 0;
        //     if(pageIndex !== 0){
        //         setPageIndex(0);

        //     }
        // }else {
        //     headerScroll = 204;
        //     tableScroll = newScrollPosition - 204;
        // }
        // if(createPoContainerRef.current)
        //     createPoContainerRef.current.scrollTop = headerScroll;

        // // Clear existing timeout
        
        // if(addPoLineTableRef.current)
        //     addPoLineTableRef.current.scrollTop = tableScroll;

    }

    // Expose orderInfoIsFilled to parent component
    useImperativeHandle(ref, () => ({
        getInitialData,
        handleScrollerDrag
        // reset
    }), [getInitialData, handleScrollerDrag]);

    return (
        <div style={{ overflowY: (!hidePoLineScroll && orderInfoIsFilled) ? 'auto' : 'hidden' }} className={clsx(styles.uploadBOMLineTable, (filteredFields?.length === 3) && styles.uploadBOMLineTableMinHeight)} onScroll={handleLineItemScroll} ref={addPoLineTableRef} onClick={() => { openAddLineTab() }}>
            <table >
                <thead>
                    <tr>
                        <th><span>LN</span></th>
                        <th><span>DESCRIPTION</span></th>
                        <th><span>QTY</span></th>
                        <th colSpan={2}><span>LINE STATUS</span></th>
                    </tr>
                </thead>
                <tbody>
                    {
                        watch('cart_items').map((item: any, _index: number) => {
                            const index = item.lineStatusIndex;
                            viewLineStatusIndex++;
                           
                            
                            return (
                                <React.Fragment key={_index}>
                                    {
                                        ((index) < bomUploadResultCopy?.length ) && <BomTile
                                            index={_index}
                                            actualIndex={index}
                                            register={register}
                                            fields={fields}
                                            products={products}
                                            setValue={setValue}
                                            watch={watch}
                                            errors={errors}
                                            control={control}
                                            getValues={getValues}
                                            userPartData={userPartData}
                                            sessionId={sessionId}
                                            searchStringData={searchStringData}
                                            setSearchString={setSearchString}
                                            setLineSessionId={setLineSessionId}
                                            lineSessionId={lineSessionId}
                                            orderInfoIsFilled={orderInfoIsFilled}
                                            setDisableBidBuyNow={setDisableBidBuyNow}
                                            openAddLineTab={openAddLineTab}
                                            setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                            hidePoLineScroll={hidePoLineScroll}
                                            setHidePoLineScroll={setHidePoLineScroll}
                                            scrollToTop={scrollToTop}
                                            bomTileDefaultData={bomUploadResult[index] ?? []}
                                            clearErrors={clearErrors}
                                            lastModifiedBomRef={lastModifiedBomRef}
                                            currentBomData={currentBomData}
                                            filterFieldsLength={filteredFields.length}
                                            filteredItemIndex={_index}
                                            validateSavedBomCreatePo={validateSavedBomCreatePo}
                                            // Optimized props for BomTile rerendering
                                            viewIndex={viewIndex}
                                            viewLineStatusIndex={viewLineStatusIndex}
                                            bomUploadResultCopy={bomUploadResultCopy}
                                            saveModifiedBom={saveModifiedBom}
                                            formErrors={formErrors}
                                            setFormErrors={setFormErrors}
                                            setBomUploadResultCopy={setBomUploadResultCopy}
                                            handleLineStatusChange={handleLineStatusChange}
                                        />

                                    }
                                </React.Fragment>
                            )
                        })
                    }
                </tbody>
            </table>
        </div>
    )
})
    
export default BomReviewTable