import React, { useState, useEffect } from 'react'
import styles from './NextPaymentDetail.module.scss'

interface PaymentDetails {
  amount: number;
  currency: string;
  paymentDate: string;
  cardType: string;
  lastFourDigits: string;
}

interface NextPaymentDetailProps {
  initialPaymentDetails?: Partial<PaymentDetails>;
  onPaymentDetailsChange?: (details: PaymentDetails) => void;
}

const NextPaymentDetail: React.FC<NextPaymentDetailProps> = ({ 
  initialPaymentDetails,
  onPaymentDetailsChange
}) => {
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails>({
    amount: 50.00,
    currency: '$',
    paymentDate: 'Sep 1, 2025',
    cardType: 'Visa',
    lastFourDigits: '4444',
    ...initialPaymentDetails
  });

  useEffect(() => {
    if (onPaymentDetailsChange) {
      onPaymentDetailsChange(paymentDetails);
    }
  }, [paymentDetails, onPaymentDetailsChange]);

  const formatAmount = (amount: number, currency: string) => {
    return `${currency}${amount.toFixed(2)}`;
  };

  const updatePaymentDetails = (updates: Partial<PaymentDetails>) => {
    setPaymentDetails(prev => ({ ...prev, ...updates }));
  };

  return (
    <div className={styles.nextPaymentContainer}>
      <div className={styles.paymentInfo}>
        <p className={styles.paymentText}>
          Your next automatic payment is for{' '}
          <span className={styles.amount}>
            {formatAmount(paymentDetails.amount, paymentDetails.currency)}
          </span>{' '}
          on {paymentDetails.paymentDate}.
        </p>
        <p className={styles.cardInfo}>
          Auto-Debit from {paymentDetails.cardType} ending in {paymentDetails.lastFourDigits}
        </p>
      </div>
    </div>
  )
}

export default NextPaymentDetail