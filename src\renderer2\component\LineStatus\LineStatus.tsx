import React, { useState, memo, useCallback } from 'react'
import styles from './LineStatus.module.scss'
import { ReactComponent as ApprovedOverlay } from '../../assets/New-images/BOM-Line-Status/APPROVED.svg';
import { ReactComponent as PendingOverlay } from '../../assets/New-images/BOM-Line-Status/PENDINg.svg';
import { ReactComponent as DeletedOverlay } from '../../assets/New-images/BOM-Line-Status/DELETED.svg';
import { ReactComponent as SkippedOverlay } from '../../assets/New-images/BOM-Line-Status/SKIPPED.svg';
import { ReactComponent as ConfidenceZero } from '../../assets/New-images/BOM-Line-Status/ConfidenceZero.svg';
import { ReactComponent as ConfidenceFifty } from '../../assets/New-images/BOM-Line-Status/ConfidenceFifty.svg';
import { ReactComponent as ConfidenceHundred } from '../../assets/New-images/BOM-Line-Status/ConfidenceHundred.svg';
import clsx from 'clsx';
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
interface LineStatusProps {
    status: string;
    setStatus: (status: string) => void;
    confidence: number;
    originalStatus: string;
    saveBomDraft: () => Promise<void>;
    isHovered: boolean;
    disableApproveButton: boolean;
    handleLineStatusChange: () => void;
}

const LineStatus = memo(({status, setStatus, confidence, originalStatus, saveBomDraft, isHovered, disableApproveButton = false, handleLineStatusChange}: LineStatusProps) => {
    const [isLineStatusHovered, setIsLineStatusHovered] = useState(false);
    const [isLineStatusFocused, setIsLineStatusFocused] = useState(false);
    const { setBOMLineStatusCountObj, bomLineStatusCountObj } = useRightWindowStore();

    const handleStatusChange = useCallback(async(newStatus: string) => {
        setStatus(newStatus);
        if(!(status === newStatus)){
            const newBOMLineStatusCountObj = {
                ...bomLineStatusCountObj,
                [newStatus]: bomLineStatusCountObj[newStatus] + 1,
                [status]: bomLineStatusCountObj[status] - 1
            };
            setBOMLineStatusCountObj(newBOMLineStatusCountObj);
        }
        await saveBomDraft();
        handleLineStatusChange();
    }, [setStatus, saveBomDraft, bomLineStatusCountObj]);

    const handleMouseEnter = useCallback(() => setIsLineStatusHovered(true), []);
    const handleMouseLeave = useCallback(() => setIsLineStatusHovered(false), []);
    const handleFocus = useCallback(() => setIsLineStatusFocused(true), []);
    const handleBlur = useCallback(() => setIsLineStatusFocused(false), []);

    return (
        <div 
            className={styles.lineStatusContainer} 
            onMouseEnter={handleMouseEnter} 
            onMouseLeave={handleMouseLeave} 
            onFocus={handleFocus} 
            onBlur={handleBlur}
        >
            <div className={styles.lineStatusOverlay}>
                {status === 'APPROVED' && <ApprovedOverlay />}
                {status === 'PENDING' && <PendingOverlay />}
                {status === 'DELETED' && <DeletedOverlay />}
                {status === 'SKIPPED' && <SkippedOverlay />}
            </div>
            <div className={clsx(styles.lineStatusConfidence, (isLineStatusHovered || isHovered || isLineStatusFocused) && styles.lineStatusConfidenceHovered)}>
                {confidence === 0 && <ConfidenceZero />}
                {confidence === 50 && <ConfidenceFifty />}
                {confidence === 100 && <ConfidenceHundred />}
            </div>
            <div className={clsx(styles.lineStatusAction, (isLineStatusHovered || isHovered || isLineStatusFocused) && styles.lineStatusActionHovered)}>
                {status === 'SKIPPED' ? 
                <button className={clsx(styles.lineStatusActionButton, originalStatus === 'PENDING' ? styles.pendingButton : styles.approveButton)} onClick={() => handleStatusChange(originalStatus)}>UN-SKIP</button>
                :
                <button className={clsx(styles.lineStatusActionButton, styles.skipButton)} onClick={() => handleStatusChange('SKIPPED')}>SKIP</button>
                }
                {status === 'DELETED' ? 
                <button className={clsx(styles.lineStatusActionButton, originalStatus === 'PENDING' ? styles.pendingButton : styles.approveButton)} onClick={() => handleStatusChange(originalStatus)}>UN-DELETE</button>
                :
                <button className={clsx(styles.lineStatusActionButton, styles.deleteButton)} onClick={() => handleStatusChange('DELETED')}>DELETE</button>
                }
                {confidence === 100 || status !== 'APPROVED' ? 
                <button className={clsx(styles.lineStatusActionButton, styles.approveButton)} onClick={() => handleStatusChange('APPROVED')} disabled={confidence === 100 || disableApproveButton}>APPROVE</button>
                :
                <button className={clsx(styles.lineStatusActionButton, originalStatus === 'PENDING' ? styles.pendingButton : styles.approveButton)} onClick={() => handleStatusChange(originalStatus)}>UN-APROV</button>
                }
            </div>
        </div>
    );
});

export default LineStatus;