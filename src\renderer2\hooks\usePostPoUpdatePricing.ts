import { useGlobalStore } from "@bryzos/giss-ui-library";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostPoUpdatePricing = () => {

  return useMutation(async (data: any) => {
    try {
      const url = `${import.meta.env.VITE_API_ORDER_SERVICE}/buyer/update-pricing`;
      const response = await axios.post(
        url,
        {data}
      );

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostPoUpdatePricing;
