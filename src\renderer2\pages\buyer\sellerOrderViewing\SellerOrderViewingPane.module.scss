.viewingPane {
  width: 100%;
  height: 100%;
  border-style: solid;
  border-width: 1px;
  border-image-source: linear-gradient(to left, #fff -5%, #1b1b21 1%);
  border-image-slice: 1;
  background-color: #191a20;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  
}

.placeholderContent {
  text-align: center;
  z-index: 1;
  position: relative;
  padding: 20px;
  
  // Optional: Add a subtle animation for the placeholder
  animation: fadeInUp 0.6s ease-out;
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  letter-spacing: 1.12px;
  text-align: center;
  color: #fff;
}

