import { RESIZE_BOX, ZOOM, ADD_BOX, DELETE_BOX, ROTATE , useBomPdfExtractorStore } from '../BomPdfExtractorStore';
import { parseSteel } from '@bryzos/steel-search-lib';
import { getValUsingUnitKey, getFloatRemainder, commomKeys, orderIncrementPrefix } from '@bryzos/giss-ui-library';

// Types and Interfaces
export interface ExtractedDataItem {
  description?: string;
  length?: string;
  grade?: string;
  quantity?: string;
  specification?: string;
  partNumber?: string;
  weight?: string;
  quantityum?: string;
  qtyUnit?: string;
  pageNumber: number;
}

export interface ProductData {
  Product_ID: string;
  id: string;
  [key: string]: any;
}

export interface SearchResultObject {
  confidence: number | null;
  productIds: string[];
  searchKeywords: string;
  status: string;
  extractedString: string;
  splitString: string;
}

export interface ConfidenceRange {
  min_match_count: number;
  max_match_count: number | null;
  confidence: number;
}

export interface BomContext {
  fileName: string;
  s3Url: string;
  bomUploadID: string;
  numPages: number;
  domesticOnly: boolean;
  uploadBomInitialData: any;
}

export interface ProcessingResult {
  success: boolean;
  results: any[];
  gridData: any[];
  hasData: boolean;
  error?: string;
}

// Constants
export const Constants = {
  PENDING: "PENDING",
  APPROVED: "APPROVED"
};

// Utility Functions
export function getQtyUnit(qtyUnit: string): string {
  switch(qtyUnit?.toLowerCase()) {
    case 'ft':
      return 'Ft';
    case 'lb':
      return 'Lb';
    case 'ea':
      return 'PC';
    case 'pc':
      return 'PC';
    case 'net tons':
      return 'Net Ton';
    case 'cwt':
      return 'CWT';
    default:
      return 'PC';
  }
}

// Data Validation Functions
export function isValidExtractedDataItem(item: ExtractedDataItem): boolean {
  const hasDescription = !!(item?.description && (item.description + '').trim() !== '');
  const hasLength = !!(item?.length && (item.length + '').trim() !== '');
  const hasGrade = !!(item?.grade && (item.grade + '').trim() !== '');
  const hasQuantity = !!(item?.quantity && (item.quantity + '').trim() !== '');
  const hasSpecification = !!(item?.specification && (item.specification + '').trim() !== '');
  const hasPartNumber = !!(item?.partNumber && (item.partNumber + '').trim() !== '');
  const hasWeight = !!(item?.weight && (item.weight + '').trim() !== '');

  return hasDescription || hasLength || hasGrade || hasQuantity || hasSpecification || hasPartNumber || hasWeight;
}

export function filterValidExtractedData(extractedData: ExtractedDataItem[]): ExtractedDataItem[] {
  return extractedData.filter(item => isValidExtractedDataItem(item));
}

// Search and Parsing Functions
export function buildSearchCriteria(item: ExtractedDataItem): string {
  return [
    item.description || '',
    item.length || '',
    item.specification || '',
    item.grade || ''
  ].filter(Boolean).join(' ');
}

export function buildSplitString(splitObj: any): string {
  return [
    splitObj.shape || '',
    splitObj.dims || '',
    splitObj.length || '',
    splitObj.grade || ''
  ].filter(Boolean).join(' ');
}

export function isValidSteelParse(splitObj: any): boolean {
  return !!(splitObj.shape || splitObj.dims || splitObj.length || splitObj.grade);
}

// Product Matching Functions
export function searchProductMatches(searchCriteria: string, productSearcher: any): any {
  const splitObj = parseSteel(searchCriteria);
  return productSearcher.search(splitObj, true);
}

export function calculateConfidence(searchResults: any[], confidenceRange: ConfidenceRange[]): number {
  if (searchResults.length === 1 && searchResults[0]?.exact_match) {
    return 100;
  } else if (searchResults.length === 1) {
    return confidenceRange[0].confidence;
  } else if (searchResults.length > 1) {
    for (let i = 0; i < confidenceRange.length; i++) {
      if (confidenceRange[i].min_match_count <= searchResults.length) {
        if (confidenceRange[i].max_match_count === null || confidenceRange[i].max_match_count >= searchResults.length) {
          return confidenceRange[i].confidence;
        }
      }
    }
    return 0;
  } else if (searchResults.length === 0) {
    return 0;
  }
  return 0;
}

export function createSearchResult(
  searchCriteria: string,
  splitString: string,
  searchResults: any[],
  confidenceRange: ConfidenceRange[]
): SearchResultObject {
  const confidence = calculateConfidence(searchResults, confidenceRange);
  return {
    confidence,
    productIds: searchResults.map((item: any) => item.Product_ID),
    searchKeywords: searchCriteria,
    status: confidence === 100 ? Constants.APPROVED : Constants.PENDING,
    extractedString: searchCriteria,
    splitString
  };
}

// Quantity Validation Functions
export function getQuantityUnit(item: ExtractedDataItem): string {
  const qtyUnit = item?.quantityum ? item.quantityum.toLowerCase() : item.qtyUnit;
  return getQtyUnit(qtyUnit || '');
}

export function validateQuantityIncrement(
  quantity: string,
  productData: ProductData[],
  productId: string,
  quantityUnit: string
): boolean {
  if (!quantity || !productId) return false;

  const product = productData.find(item => item.Product_ID === productId);
  if (!product) return false;

  const orderIncrement = getValUsingUnitKey(product, quantityUnit.toLowerCase(), orderIncrementPrefix);
  if (!orderIncrement) return true;

  const numQuantity = parseFloat(quantity);
  if (numQuantity > 0 && getFloatRemainder(numQuantity, orderIncrement) !== 0) {
    return false;
  }

  return true;
}

export function adjustConfidenceForQuantity(
  confidence: number,
  hasQuantity: boolean,
  isValidQuantity: boolean
): number {
  if (!hasQuantity && confidence === 100) {
    return 50;
  } else if (hasQuantity && confidence === 100 && !isValidQuantity) {
    return 50;
  }
  return confidence;
}

// Result Building Functions
export function createResultObject(
  extractedItem: ExtractedDataItem,
  searchResult: SearchResultObject,
  quantityUnit: string,
  quantity: string | null,
  numPages: number,
): any {
  //const descriptionMatches  =  searchResult.confidence === 100;
  
  return {
    original_line_status: searchResult.confidence === 100 ? Constants.APPROVED : Constants.PENDING,
    status: searchResult.confidence === 100 ? Constants.APPROVED : Constants.PENDING,
    confidence: searchResult.confidence,
    product_tag: extractedItem.partNumber ? extractedItem.partNumber : null,
    description: extractedItem.description,
    specification: extractedItem.specification,
    search_string: searchResult.searchKeywords,
    pdf_string: searchResult.extractedString,
    split_string: searchResult.splitString,
    grade: extractedItem.grade,
    shape: null,
    qty: quantity,
    qty_unit: quantityUnit,
    price_per_unit: "0.0000",
    price_unit: null,
    length: extractedItem.length,
    weight_per_quantity: null,
    matched_product_count: searchResult.productIds.length === 1?1:0,
    matched_products: searchResult.productIds.length === 1? searchResult.productIds:[],
    selected_products: searchResult.productIds.length === 1? searchResult.productIds:[],//descriptionMatches ? [searchResult.productIds[0]] : [],
    current_page: extractedItem.pageNumber,
    total_pages: numPages,
    product_index: 0, // Will be set by caller
    domestic_material_only: null,
    buyer_line_total: "0.0000",
    last_updated_product: 0,
    line_weight: "0.00"
  };
}

export function createGridDataObject(
  extractedItem: ExtractedDataItem,
  productIds: string[]
): any {
  return {
    ...extractedItem,
    productIds
  };
}

// BOM Processing Functions
export function createBomUploadWrapper(
  results: any[],
  bomContext: BomContext
): any {
  return {
    "file_name": bomContext.fileName,
    "actual_file_name": bomContext.fileName,
    "status": "COMPLETED",
    "s3_url": bomContext.s3Url,
    "bom_upload_id": bomContext.bomUploadID,
    "total_pages": bomContext.numPages,
    "material_total": "0.0000",
    "sales_tax": "0.00",
    "total_weight": "0.00",
    "total_purchase": "0.00",
    "deposit": "0.00",
    "subscription": "0.00",
    "review_status": "PENDING REVIEW",
    "line1": bomContext.uploadBomInitialData.shipping_details.line1,
    "line2": bomContext.uploadBomInitialData.shipping_details.line2?.trim() || null,
    "city": bomContext.uploadBomInitialData.shipping_details.city,
    "state_id": bomContext.uploadBomInitialData.shipping_details.state_id,
    "zip": bomContext.uploadBomInitialData.shipping_details.zip,
    "bom_name": bomContext.uploadBomInitialData.internal_po_number,
    "bom_type": bomContext.uploadBomInitialData.order_type,
    "delivery_date": bomContext.uploadBomInitialData.delivery_date,
    "result": results
  };
}

export function processExtractedDataItems(
  extractedData: ExtractedDataItem[],
  productData: ProductData[],
  productSearcher: any,
  confidenceRange: ConfidenceRange[],
  bomContext: BomContext
): { results: any[], gridData: any[] } {
  const results: any[] = [];
  const gridData: any[] = [];

  const validExtractedData = filterValidExtractedData(extractedData);

  for (let index = 0; index < validExtractedData.length; index++) {
    const item = validExtractedData[index];

    // Build search criteria and parse steel
    const searchCriteria = buildSearchCriteria(item);
    const splitObj = parseSteel(searchCriteria);

    if (!isValidSteelParse(splitObj)) {
      continue;
    }

    const splitString = buildSplitString(splitObj);

    // Search for products
    const searchResults = searchProductMatches(searchCriteria, productSearcher);
    const searchResult = createSearchResult(searchCriteria, splitString, searchResults.results, confidenceRange);

    // Handle quantity validation
    const quantityUnit = getQuantityUnit(item);
    const quantity = item.quantity ? item.quantity + '' : null;
    const hasQuantity = !!item.quantity;

    let finalConfidence = searchResult.confidence;
    if (hasQuantity && finalConfidence === 100 && searchResult.productIds.length > 0) {
      const isValidQuantity = validateQuantityIncrement(quantity || '', productData, searchResult.productIds[0], quantityUnit);
      finalConfidence = adjustConfidenceForQuantity(finalConfidence, hasQuantity, isValidQuantity);
    } else if (!hasQuantity && finalConfidence === 100) {
      finalConfidence = adjustConfidenceForQuantity(finalConfidence, hasQuantity, true);
    }

    // Update search result with final confidence
    searchResult.confidence = finalConfidence;
    searchResult.status = finalConfidence === 100 ? Constants.APPROVED : Constants.PENDING;

    // Create result object
    const resultObj = createResultObject(item, searchResult, quantityUnit, quantity, bomContext.numPages);
    resultObj.product_index = index + 1;

    results.push(resultObj);
    gridData.push(createGridDataObject(item, searchResult.productIds));
  }

  return { results, gridData };
}

// Main Orchestrator Function
export async function processProductExtraction(
  productData: ProductData[],
  extractedData: ExtractedDataItem[],
  confidenceRange: ConfidenceRange[],
  bomContext: BomContext,
  productSearcher: any
): Promise<ProcessingResult> {
  try {

    // Process all extracted data items
    const { results, gridData } = processExtractedDataItems(
      extractedData,
      productData,
      productSearcher,
      confidenceRange,
      bomContext
    );


    return {
      success: true,
      results,
      gridData,
      hasData: results.length > 0
    };
  } catch (error) {
    console.error(error);

    return {
      success: false,
      results: [],
      gridData: [],
      hasData: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

export const createActions = (action: any, beforeState: any, afterState: any) => {
    const { setUndoStack, setRedoStack } = useBomPdfExtractorStore.getState() as any;
    const undoAction: any = { state: beforeState, action: null };
    const redoAction = { action: action, state: afterState };

    switch(action){
        case ADD_BOX:
            undoAction.action = DELETE_BOX;
        break;
        case DELETE_BOX:
            undoAction.action = ADD_BOX;
        break;
        //No need to change action for below actions
        case RESIZE_BOX:
        case ZOOM:
        case ROTATE:
            undoAction.action = action;
        break;
    }
    setUndoStack((prev: any) => [...prev, {undoAction, redoAction}]);
    setRedoStack([]);
  };