import { getFloatRemainder, getValUsingUnitKey, newPricingPrefix, orderIncrementPrefix, priceUnits, useCalculateDeposit, useCalculateSalesTax } from "@bryzos/giss-ui-library";
import { calculateTotalWeightForProduct } from "../utility/pdfUtils";
import { getPriceExample } from "../utility/priceIntegratorExample";
import { useEffect, useState } from "react";
import { useDebouncedValue } from "@mantine/hooks";

export const useCalculatePoPrices = ({ watch, setValue, getValues, register, clearErrors, trigger, setError, userPartData, setHandleSubmitValidation, isAddLineBtnClicked }) => {

    const [debouncedTotalWeight] = useDebouncedValue(watch('total_weight'), 400);
    const [debouncedMatTotal] = useDebouncedValue(watch('material_total'), 400);
    const [salesTaxCounter, setSalesTaxCounter] = useState(0);
    const calculateSalesTax = useCalculateSalesTax();
    const calculateDepositTax = useCalculateDeposit();

    useEffect(() => {
        console.log('debouncedTotalWeight', debouncedTotalWeight, watch('cart_items'));
        if (debouncedTotalWeight && watch('cart_items')) {
            handlePriceIntegration();
        }else{
            calculateMaterialTotalPrice();
        }
    }, [debouncedTotalWeight])

    useEffect(() => {
        calculateTotalPrice();
    }, [watch('material_total'), watch('sales_tax'), watch('deposit')]);

    useEffect(() => {
        if(watch('material_total') && watch('shipping_details.zip') && watch('shipping_details.state_id')){
            setHandleSubmitValidation(false);
            calculateTotalPricing().then(()=>{
                setHandleSubmitValidation(true);
            });
        }
        else{
                setValue("sales_tax", parseFloat('0'));
                setValue("deposit", parseFloat('0'));
        }
    }, [debouncedMatTotal])

    const handlePriceIntegration = async (index?: number) => {
        console.log('handlePriceIntegration', watch(`cart_items`));
        const totalWeight = calculateTotalWeightForProduct(watch(`cart_items`));
        setValue('total_weight', totalWeight); 
        const productIdList = [];
        if(index){
            productIdList.push(watch(`cart_items.${index}.descriptionObj.Product_ID`)); 
        }
        else{
            for (const [index, item] of watch('cart_items').entries()) {
                if (isAddLineBtnClicked) {
                    if (index > watch('lastIndex')) {
                        productIdList.push(item.descriptionObj.Product_ID);
                    }
                } else if (item?.descriptionObj?.Product_ID) {
                    productIdList.push(item.descriptionObj.Product_ID);
                }
            }
        }
        const searchZipCode = watch(`shipping_details.zip`) 
        if(productIdList.length === 0) return {};
        let netTotalWeight = totalWeight;
        if(netTotalWeight <= 500){
            netTotalWeight = 500;
        }
        const productPricesData = await getPriceExample(productIdList, searchZipCode, Math.floor(netTotalWeight));
        if(index){
            updateValue(index, productPricesData)
            // saveUserLineActivity(sessionId, false, index);
        }else{
            for(let i = 0; i < watch('cart_items').length; i++){
                if(isAddLineBtnClicked){
                    if(i > watch('lastIndex')){
                        updateValue(i, productPricesData)
                    }
                }else{
                    updateValue(i, productPricesData)
                }
                // if(watch(`cart_items.${i}.descriptionObj`)){
                //     saveUserLineActivity(sessionId, false, i);
                // }
            }
        }
    }

    const updateValue = async (index: number, priceMapping: any = {}) => {
        if (watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0) {

            const _selected = getValues(`cart_items.${index}`).descriptionObj;
            const productId = watch(`cart_items.${index}.descriptionObj.Product_ID`)
            const priceUnit = getValues(`cart_items.${index}.price_unit`);

            resetPricePerUnitFields(index, _selected)
            const prices = priceMapping[productId];

            const pumDropdownOptions = watch(`cart_items.${index}.price_um`);
            pumDropdownOptions.forEach((item: any) => {
                if (item.toLowerCase() !== "net ton" || item.toLowerCase() !== priceUnits.net_ton) {
                    setValue(`cart_items.${index}.descriptionObj.${newPricingPrefix}${item}`, prices?.[item] || prices?.[item.toLowerCase()])
                }
            })
            if (_selected && Object.keys(_selected).length > 0) {
                const updateQtyUnit = watch(`cart_items.${index}.qty_unit`).toLowerCase();
                const updatePriceUnit = watch(`cart_items.${index}.price_unit`).toLowerCase();
                setValue(`cart_items.${index}.qty_unit`, updateQtyUnit);
                setValue(`cart_items.${index}.price_unit`, updatePriceUnit);
                updateLineItem(index);
                const qtyVal = watch(`cart_items.${index}.qty`);
                const qtyUnit = watch(`cart_items.${index}.qty_unit`);
                const priceUnit = watch(`cart_items.${index}.price_unit`);
                if (!qtyUnit) {
                    setValue(`cart_items.${index}.qty_um`, _selected.QUM_Dropdown_Options.split(","));
                    setValue(`cart_items.${index}.qty_unit`, _selected.QUM_Dropdown_Options.split(",")[0].toLowerCase());
                }
                const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options.split(",")[0] : '')).toLowerCase();
                const updatedUnit = unit;
                const orderIncrement = getValUsingUnitKey(_selected, updatedUnit, orderIncrementPrefix);
                if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                    if (!priceUnit) resetPricePerUnitFields(index, _selected)
                } else {
                    if (qtyVal < 1) clearErrors(`cart_items.${index}.qty`);
                    setValue(`cart_items.${index}.price_unit`, '');
                    setValue(`cart_items.${index}.price`, 0);
                }
                calculateMaterialTotalPrice();
            }
        }
    }

    const updateLineItem = (index: any) => {
        const _selected = getValues(`cart_items.${index}.descriptionObj`);

        if (_selected) {
            const qtyVal = +getValues(`cart_items.${index}.qty`) || 0;
            const qtyUnit = getValues(`cart_items.${index}.qty_unit`);
            const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options.split(",")[0] : '')).toLowerCase();
            const updatedUnit = unit;
            const orderIncrement = getValUsingUnitKey(_selected, updatedUnit, orderIncrementPrefix);
            pricePerUnitChangeHandler(index, undefined);
            if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                const buyerPricePerUnit = parseFloat(getValUsingUnitKey(_selected, unit, newPricingPrefix));
                setValue(`cart_items.${index}.buyer_calculation_price`, buyerPricePerUnit);
                const totalVal = parseFloat((buyerPricePerUnit * qtyVal).toString()).toFixed(2);
                const extendedValue = buyerPricePerUnit * qtyVal;
                setValue(`cart_items.${index}.extended`, +totalVal);
                setValue(`cart_items.${index}.extendedValue`, +extendedValue);
                clearErrors(`cart_items.${index}.qty`);
                trigger(`cart_items.${index}.qty`);
            } else {
                if (_selected) setError(`cart_items.${index}.qty`, { message: `Quantity can only be multiples of ${orderIncrement}` })
                if (qtyVal === null) setValue(`cart_items.${index}.qty`, '');
                setValue(`cart_items.${index}.extended`, 0);
            }
        }
    }

    const resetPricePerUnitFields = (index, product) => {
        const priceUnit = getValues(`cart_items.${index}.price_unit`);
        const priceUnitMData = product?.PUM_Dropdown_Options?.split(",").filter((item: any) => !(item.trim().toLowerCase() === priceUnits.net_ton || item.trim().toLowerCase() === 'net ton'));
        setValue(`cart_items.${index}.price_um`, priceUnitMData);
        if (!priceUnit) setValue(`cart_items.${index}.price_unit`, priceUnitMData[0])
        pricePerUnitChangeHandler(index, product);
    }

    const pricePerUnitChangeHandler = (index: any, product: any, priceData: any = 0) => {
        product = product ?? getValues(`cart_items.${index}.descriptionObj`);
        if (product) {
            const unit = getValues(`cart_items.${index}.price_unit`) || product.PUM_Dropdown_Options.split(",")[0];
            const umVal = getValUsingUnitKey(product, unit, newPricingPrefix);
            setValue(`cart_items.${index}.price`, umVal);
        }
        else {
            setValue(`cart_items.${index}.price`, 0);
        }
    }

    const updateLineProduct = (index, product) => {

        // setSelectedProduct(product);

        setValue(`cart_items.${index}.qty`, '')
        setValue(`cart_items.${index}.extended`, 0)
        setValue(`cart_items.${index}.price`, 0)
        setValue(`cart_items.${index}.price_unit`, '')
        setValue(`cart_items.${index}.qty_unit`, '')
        setValue(`cart_items.${index}.domesticMaterialOnly`, null)
        clearErrors(`cart_items.${index}.qty`)
        if (product) {
            // resetQtyAndPricePerUnitFields(index, product);
            if (userPartData && Object.keys(userPartData)?.length) {
                setValue(`cart_items.${index}.product_tag`, userPartData[product?.Product_ID])
            }
        }
        else {
            calculateMaterialTotalPrice();
        }
    }

    const calculateMaterialTotalPrice = () => {
        const items = getValues(`cart_items`) ?? [];
        const totalPurchase = items.reduce((accumaltaor: any, product) => {
            accumaltaor.buyerTotalPurchase += product.extended ?? 0;
            return accumaltaor;
        }, { buyerTotalPurchase: 0 });
        setValue(`material_total`, String(totalPurchase.buyerTotalPurchase));
        trigger();
    }

    const quantitySizeValidator = (e: any, index: any) => {
        if (isNaN(e.target.value)) {
            return;
        }
        if (e.target.value) {
            const arr = e.target.value.split(".");

            if (arr.length > 1) {
                // e.target.value = arr[0].slice(0, 8) + "." + arr[1].slice(0, 2);
                e.target.value = arr[0].slice(0, 8) + "." + arr[1];
                setValue(`cart_items.${index}.qty`, e.target.value);
            } else {
                e.target.value = arr[0].slice(0, 8);
                setValue(`cart_items.${index}.qty`, e.target.value);
            }
        }
        register(`cart_items.${index}.qty`).onChange(e);
    };


    const calculateTotalPrice = () => {
        const materialTotal = +(watch('material_total') ?? 0);
        let totalPurchaseOrderPrice: number = 0;
        if (materialTotal) {
            totalPurchaseOrderPrice += materialTotal + (+(watch('sales_tax') ? watch('sales_tax') : 0)) + (+(watch('deposit') ?? 0))
        }
        setValue('totalPurchase', parseFloat(totalPurchaseOrderPrice));
    }
    
    const calculateTotalPricing = async() => {
      await handleSalesTax();
      let depositValue = 0;
      if (getValues("payment_method") === "ach_credit") {
      depositValue = await calculateDepositAmount();
      }
      setValue("deposit", depositValue);
    }
  
    const handleSalesTax = async () => {
      try{
          const matTotal = getValues('material_total');
          if (!matTotal) {
              return;
          }
          const taxCounter = salesTaxCounter + 1;
          setSalesTaxCounter(taxCounter)
          let payloadData:any = {...getValues()};
          payloadData.cart_items = payloadData.cart_items?.map((item: any) =>{
              if(!item.descriptionObj)return null;
              const itemCopy = {...item}
              itemCopy.descriptionObj = undefined;
              return itemCopy
          }).filter(Boolean)
          payloadData.recevingHours= undefined;
          payloadData.deposit= undefined;
          payloadData.totalPurchase= undefined;
          payloadData.salesTaxCounter = salesTaxCounter + 1;
          payloadData.price = payloadData.material_total;
          const payload = {
              data: payloadData,
          };
          const salesResponse = await calculateSalesTax.mutateAsync(payload);
          const salesTaxCounterResponse = salesResponse.data.data.salesTaxCounter;
          if (salesTaxCounterResponse === taxCounter && watch('material_total')) {
            setValue("sales_tax", parseFloat(salesResponse.data.data.tax));
            
          }
      }catch(error){
    
      }
    };
    
    const calculateDepositAmount = async () => {
      try {
        const materialTotal = parseFloat(getValues("material_total")??"0").toFixed(2)
        const payload = {
          data: {
            payment_method: getValues("payment_method"),
            price: Number(materialTotal),
          },
        };
        const depositResponse = await calculateDepositTax.mutateAsync(payload);
        return depositResponse.data.data.deposit_amount;
      } catch (error) {
          console.error(error)
      }
    };
    return {
        handlePriceIntegration,
        updateValue,
        updateLineItem,
        resetPricePerUnitFields,
        pricePerUnitChangeHandler,
        updateLineProduct,
        calculateMaterialTotalPrice,
        quantitySizeValidator,
        calculateTotalPrice
    };
}
