<svg width="284" height="66" viewBox="0 0 284 66" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_61_882)">
<rect x="1" y="1" width="282" height="64" rx="12" fill="url(#paint0_linear_61_882)"/>
</g>
<rect x="0.5" y="0.5" width="283" height="65" rx="12.5" stroke="url(#paint1_linear_61_882)"/>
<defs>
<filter id="filter0_i_61_882" x="0" y="0" width="286.21" height="68.2099" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="5"/>
<feGaussianBlur stdDeviation="1.10497"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_61_882"/>
</filter>
<linearGradient id="paint0_linear_61_882" x1="7.49344" y1="-130.765" x2="286.993" y2="144.713" gradientUnits="userSpaceOnUse">
<stop stop-color="#1C40E7"/>
<stop offset="1" stop-color="#16B9FF"/>
</linearGradient>
<linearGradient id="paint1_linear_61_882" x1="173.454" y1="185.941" x2="146.282" y2="20.6779" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#101015"/>
</linearGradient>
</defs>
</svg>
