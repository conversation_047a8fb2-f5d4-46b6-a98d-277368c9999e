.orderConfirmationContainer {
    padding: 32px 99px 68px;
    width: 100%;
    position: relative;
    background-color: #191a20;
    &::before {
        content: "";
        position: absolute;
        top: 1px;
        left: 0;
        width: 50%;
        height: 1px;
        background: linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(0, 0, 0, 0)); 
        z-index: 11;
      }
    .orderConfirmationContent {
        display: flex;
        flex-direction: column;
        text-align: center;
        color: #fff;
        border-radius: 16px;
        background: url(../../assets/New-images/MainBg.svg) no-repeat;
        background-size: cover;
      
        .orderConfirmationPoDetails{
            width: 100%;
            height: 111px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            .orderConfirmdText {
                font-family: Syncopate;
                font-size: 26px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.35;
                letter-spacing: 1.04px;
                text-align: center;
                color: #fff;
            }
            .poNumberText {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                gap: 8px;
                flex-direction: row;
                text-align: left;
                color: #fff;
                line-height: 1;
                font-stretch: normal;
                font-style: normal;
                .poText {
                    font-family: Syncopate;
                    font-size: 14px;
                    font-weight: bold;
                    letter-spacing: normal;
                }
                .jobText {
                    font-family: Inter;
                    font-size: 16px;
                    font-weight: 300;
                    letter-spacing: normal;
                }
                .poNumber{
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: 200;
                    letter-spacing: 1.26px;
                    
                }
            }
        }
        .purchaseRating{
            width: 100%;
            height: 94px;
            box-shadow: inset 8px 9px 4px -3px #000;
            // background-image: linear-gradient(149deg, #0f0f14 -7%, #393e47 109%);
            background: url(../../assets/New-images/RatingBG.svg) no-repeat;
           
            flex-direction: row;
            display: flex;
            align-items: center;
            justify-content: end;
            padding: 24px 40px;
            gap: 56px;
            background-size: cover;
            background-position: bottom;
            p{
                font-family: Syncopate;
                font-size: 18px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: -0.18px;
                text-align: left;
                color: #c3c4ca;
                text-transform: uppercase;
            }
            .GiveUsthumbsUp{
                font-family: Inter;
                font-size: 18px;
                font-weight: normal;
                font-stretch: normal;
                font-style: italic;
                line-height: 1.3;
                letter-spacing: normal;
                text-align: center;
                color: #c3c4ca;
                text-transform:none;
            }
            .purchaseRatingBtn{
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
            }
        }
        .purchaseEmailDiv{
            display: flex;
            gap: 32px;
            align-items: center;
            justify-content: center;
            height: 100px;
            // border: solid 1px transparent;
            // background: linear-gradient(#19191e, #19191e) padding-box, linear-gradient(to bottom right, rgba(0, 0, 0, 0) 40%, rgba(255, 255, 255, 0.2901960784) 85%) border-box;
            // border-radius: 0px 0px 16px 16px;
            .purchaseEmailTextDiv {
                display: flex;
                flex-direction: column;
                .purchaseEmailText {
                    font-family: Syncopate;
                    font-size: 16px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.3;
                    letter-spacing: normal;
                    text-align: center;
                    color: rgba(255, 255, 255, 0.8);
                    text-transform: uppercase;
                }
                .emailIdInvoice {
                    font-family: Inter;
                    font-size: 16px;
                    font-weight: 200;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.3;
                    letter-spacing: 1.28px;
                    text-align: left;
                    color: #fff;
                }
            }
        }
    }
    .orderConfirmationDefaultText {
        padding: 48px 7px;
        font-family: Inter;
        font-size: 20px;
        font-weight: 200;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.65;
        letter-spacing: -0.2px;
        text-align: center;
        color: #fff;
    }
    .orderConfirmationButtons {
        display: flex;
        flex-direction: column;
        gap: 16px;
        .orderConfirmationButtonBg {
            border-radius: 14px;
            background-color: rgba(255, 255, 255, 0.04);
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            &:hover {
                .orderConfirmationButtonText {
                    color: #fff;
                    button {
                        color: #fff;
                    }
                }
                .emailIdInter {
                    color: #fff;
                }
            }
            .orderConfirmationButtonText {
                font-family: Syncopate;
                font-size: 16px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: -0.64px;
                text-align: center;
                color: rgba(255, 255, 255, 0.4);
                text-transform: uppercase;
                button {
                    font-family: Syncopate;
                    font-size: 16px;
                    font-weight: bold;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.3;
                    letter-spacing: -0.64px;
                    text-align: center;
                    color: rgba(255, 255, 255, 0.4);
                    text-transform: uppercase;
                }
            }
            .emailIdInter {
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: normal;
                text-align: center;
                color: rgba(255, 255, 255, 0.4);
            }
            .uploadYourPoDiv {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }
            label{
                width: 100%;
                cursor: pointer;
               
            }
    
            .disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
    
            input {
                display: none;
            }    
        }
    }
    .pointer {
        cursor: pointer;
    }
    .createAnotherPurchase{
        border: 0px;
        height: 67px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 40px;
        border-radius: 10px;
        background: url(../../assets/New-images/CreateDefault.svg) no-repeat ; 
        background-size: cover;
        width: 100%;
        background-position: center;    
        transition: background-image 0.2s ease-in-out;
        &:hover {
            background: url(../../assets/New-images/CreateDefaultHover.svg) no-repeat ; 
            background-size: cover;
            width: 100%;
            background-position: center;  
        }
    }
}
.thumbsImages {
    .img1 {
        display: block;
    }
    .img2 {
        display: none;
    }
    &:hover {
        .img1 {
            display: none;
        }
        .img2 {
            display: block;
        }
    }
}
.thumbsImagesActive {
    .img1 {
        display: none;
    }
    .img2 {
        display: block;
    }
}

.ConfirmationDialog {
    .dialogContent {
        max-width: 300px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 30px 34px 30px 34px;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(24px);
        backdrop-filter: blur(24px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;

        p {
            margin-bottom: 20px;
        }


        .submitBtn {
            transition: all 0.1s;
            font-family: Noto Sans;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            color: #70ff00;
            background-color: transparent;
            border: 0;
            opacity: 0.7;

            &:disabled {
                cursor: not-allowed;
                color: #fff !important;
                opacity: 0.5 !important;
            }

            &:hover {
                color: #70ff00;
                opacity: unset;
            }
        }

        .cancelBtn {
            opacity: 0.7;
            font-family: Noto Sans;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            color: #fff;
            transition: all 0.1s;
            background-color: transparent;
            border: none;

            &:hover {
                opacity: unset;
            }
        }

        p {
            padding: 20px;
        }
    }

    .actionsTab {
        display: flex;
        width: 100%;

        button {
            width: 50%;
        }
    }
}
