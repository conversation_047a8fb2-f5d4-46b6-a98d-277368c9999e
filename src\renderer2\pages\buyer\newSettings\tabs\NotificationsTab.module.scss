.notificationMainContainer{
    .notificationHeader{
      padding: 24px 0px;
      font-family: Inter;
      font-size: 14px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.5;
      letter-spacing: normal;
      text-align: left;
      color: #fff;
      .methodText {
        font-weight: bold;
      }
    }
    .methodIcon {
      height: 16px;
      display: inline-flex;
      width: 16px;
      align-items: center;
      justify-content: center;
      border-radius: 3px;
      font-family: Inter;
      font-size: 13px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      letter-spacing: 0.52px;
      text-align: center;
      color: #0f0f14;
      border: 1px solid transparent;
    }
    .textIcon {
      background-color: #32ff6c;
    }
    .emailIcon {
      background-color: #ffc44f;
    }
    .desktopIcon {
      background-color: #459fff;
    }
    .notificationContainer {
      display: flex;
      flex-direction: column;
      gap: 16px;
      .notificationSection {
        display: flex;
        width: 100%;
        .notificationSectionTitle {
          width: 40%;
          border-right: solid 1px #191a20;
          background-color: rgba(255, 255, 255, 0.04);
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 10px 0px 0px 10px;
          font-family: Syncopate;
          font-size: 18px;
          font-weight: bold;
          font-stretch: normal;
          font-style: normal;
          line-height: 1;
          letter-spacing: 1.26px;
          text-align: left;
          color: #fff;
        }
        .notificationSectionContent {
          flex: 1;
          background-color: rgba(255, 255, 255, 0.04);
          border-radius: 0px 10px 10px 0px;
          .notificationItem {
            padding: 8px 24px 8px 16px;
            border-bottom: solid 1px #191a20;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.02;
            letter-spacing: 0.56px;
            text-align: left;
            color: #fff;
            text-transform: uppercase;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 32px;
            &:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
  }
  .notificationCheckbox {
    width: 16px;
    height: 16px;
    font-family: Inter;
    font-size: 13px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.52px;
    text-align: center;
    color: #9b9eac;
    border-radius: 3px;
    border: solid 1px #9b9eac;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  
    &:hover {
      color: #fff;
      border: solid 1px #fff;
    }
  
    &.textType {
      &.checked {
        background-color: #32ff6c;
        color: #0f0f14;
        border: solid 1px transparent;
      }
    }
  
    &.emailType {
      &.checked {
        background-color: #ffc44f;
        color: #0f0f14; 
        border: solid 1px transparent;
      }
    }
  
    &.desktopType {
      &.checked {
        background-color: #459fff;
        color: #0f0f14;
        border: solid 1px transparent;
      }
    }
  }
  .notificationToggle {
    display: flex;
    gap: 24px;
  }