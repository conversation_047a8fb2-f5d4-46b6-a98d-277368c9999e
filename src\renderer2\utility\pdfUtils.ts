import { orderIncrementPrefix, priceUnits } from "@bryzos/giss-ui-library";
import { formatToTwoDecimalPlaces } from "../helper";

export const descriptionLines = (description) => {
    const descriptionArray = description.split('\n');
    const firstDescriptionLine = descriptionArray[0] + "\r";
    return firstDescriptionLine;
}

export const getOtherDescriptionLines = (description) => {
    const descriptionArray = description.split('\n');
    const otherDescriptionLines = descriptionArray?.slice(1)?.join(" \r"); 
    return otherDescriptionLines;
};

export const calculateSellerTotalOrderWeight = (items) => {
    let totalOrderWeights = 0;
    items?.forEach((item) => {
        totalOrderWeights += +item.total_weight;  
    });
    return formatToTwoDecimalPlaces(totalOrderWeights); 
};

export const calculateBuyerTotalOrderWeight = (dataArray) => {
    let totalOrderWeights = 0;
    dataArray?.forEach((data) => {
        let lineWeight = 0;
        const qty = data.qty ? parseFloat(data.qty.replace(/[\$,]/g, '')) : 0;
        if(data?.descriptionObj && Object.keys(data?.descriptionObj).length > 0 && data?.lineStatus !== 'SKIPPED'){
            const unit = (data.qty_unit || (data.descriptionObj.QUM_Dropdown_Options ? data.descriptionObj.QUM_Dropdown_Options.split(",")[0] : '')).toLowerCase();

            const orderIncrementLb = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.lb}`].replace(/[\$,]/g, '')) 
            const orderIncrementFt = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.ft}`].replace(/[\$,]/g, ''));
            const lbsPerFt = orderIncrementLb / orderIncrementFt
            const orderIncrementFtPrice = lbsPerFt * orderIncrementFt
            const updatedUnit = unit.toLowerCase() === priceUnits.ea ? priceUnits.pc : unit;
            const actualOrderIncrement = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${updatedUnit}`].replace(/[\$,]/g, ''));
            lineWeight = orderIncrementFtPrice * qty / actualOrderIncrement;
        }
        totalOrderWeights = +(formatToTwoDecimalPlaces(totalOrderWeights).replace(/[\$,]/g, "")) + +(formatToTwoDecimalPlaces(lineWeight).replace(/[\$,]/g, ""));
    });

    return totalOrderWeights;
}

export const calculateBuyerTotalOrderWeightForGear = (dataArray) => {
    let totalOrderWeights = 0;
    dataArray?.forEach((data) => {
        let lineWeight = 0;
        const qty = data.qty ? parseFloat(data.qty.replace(/[\$,]/g, '')) : 0;
        if(data?.descriptionObj && Object.keys(data?.descriptionObj).length > 0 && data?.lineStatus !== 'SKIPPED' && data.qty  && data.price && data.price > 0){
            const unit = (data.qty_unit || (data.descriptionObj.QUM_Dropdown_Options ? data.descriptionObj.QUM_Dropdown_Options.split(",")[0] : '')).toLowerCase();
            const orderIncrementLb = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.lb}`].replace(/[\$,]/g, '')) 
            const orderIncrementFt = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.ft}`].replace(/[\$,]/g, ''));
            const lbsPerFt = orderIncrementLb / orderIncrementFt
            const orderIncrementFtPrice = lbsPerFt * orderIncrementFt
            const updatedUnit = unit.toLowerCase() === priceUnits.ea ? priceUnits.pc : unit;
            const actualOrderIncrement = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${updatedUnit}`].replace(/[\$,]/g, ''));
            lineWeight = orderIncrementFtPrice * qty / actualOrderIncrement;
        }
        totalOrderWeights = +(formatToTwoDecimalPlaces(totalOrderWeights).replace(/[\$,]/g, "")) + +(formatToTwoDecimalPlaces(lineWeight).replace(/[\$,]/g, ""));
    });

    return totalOrderWeights;
}

export const calculateTotalWeightForProduct = (dataArray) => {
    let totalOrderWeights = 0;
    dataArray?.forEach((data: any) => {
        let lineWeight = 0;
        const qty = data.qty ? parseFloat(data.qty.replace(/[\$,]/g, '')) : 0;
        if(data?.descriptionObj && Object.keys(data?.descriptionObj).length > 0 && data?.lineStatus !== 'SKIPPED' && qty > 0){
            if (data?.descriptionObj) {
                const unit = (data.qty_unit || (data.descriptionObj.QUM_Dropdown_Options ? data.descriptionObj.QUM_Dropdown_Options.split(",")[0] : '')).toLowerCase();
                const orderIncrementLb = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.lb}`].replace(/[\$,]/g, '')) 
                const orderIncrementFt = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.ft}`].replace(/[\$,]/g, ''));
                const lbsPerFt = orderIncrementLb / orderIncrementFt
                const orderIncrementFtPrice = lbsPerFt * orderIncrementFt
                const updatedUnit = unit.toLowerCase() === priceUnits.ea ? priceUnits.pc : unit;
                const actualOrderIncrement = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${updatedUnit}`].replace(/[\$,]/g, ''));
                lineWeight = orderIncrementFtPrice * qty / actualOrderIncrement;
            }
        }
        totalOrderWeights = +(formatToTwoDecimalPlaces(totalOrderWeights).replace(/[\$,]/g, "")) + +(formatToTwoDecimalPlaces(lineWeight).replace(/[\$,]/g, ""));
    });
    return totalOrderWeights;
}