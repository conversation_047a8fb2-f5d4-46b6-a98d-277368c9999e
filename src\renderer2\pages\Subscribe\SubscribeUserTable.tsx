import React, { useState } from 'react';
import styles from './SubscribeUserTable.module.scss';

interface User {
  id: string;
  userName: string;
  email: string;
  license: 'Assigned' | 'Unassigned';
  action: string;
  status: string;
}

interface SubscribeUserTableProps {
  initialUsers?: User[];
  onUserUpdate?: (users: User[]) => void;
}

const SubscribeUserTable: React.FC<SubscribeUserTableProps> = ({
  initialUsers,
  onUserUpdate
}) => {
  const [users, setUsers] = useState<User[]>(initialUsers || [
    {
      id: '1',
      userName: 'Shep Hickey',
      email: '<EMAIL>',
      license: 'Assigned',
      action: 'Select Action',
      status: 'Active'
    },
    {
      id: '2',
      userName: '<PERSON>',
      email: '<EMAIL>',
      license: 'Unassigned',
      action: 'Select Action',
      status: 'Pending'
    }
  ]);

  const [editingCell, setEditingCell] = useState<{
    userId: string;
    field: 'userName' | 'email';
  } | null>(null);

  const [tempValue, setTempValue] = useState<string>('');

  const handleCellClick = (userId: string, field: 'userName' | 'email', currentValue: string) => {
    setEditingCell({ userId, field });
    setTempValue(currentValue);
  };

  const handleInputChange = (value: string) => {
    setTempValue(value);
  };

  const handleInputBlur = () => {
    if (editingCell) {
      const updatedUsers = users.map(user => 
        user.id === editingCell.userId 
          ? { ...user, [editingCell.field]: tempValue }
          : user
      );
      setUsers(updatedUsers);
      onUserUpdate?.(updatedUsers);
    }
    setEditingCell(null);
    setTempValue('');
  };

  const handleInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleInputBlur();
    }
    if (e.key === 'Escape') {
      setEditingCell(null);
      setTempValue('');
    }
  };

  const handleLicenseChange = (userId: string, newLicense: 'Assigned' | 'Unassigned') => {
    const updatedUsers = users.map(user => 
      user.id === userId 
        ? { ...user, license: newLicense }
        : user
    );
    setUsers(updatedUsers);
    onUserUpdate?.(updatedUsers);
  };

  const handleActionChange = (userId: string, action: string) => {
    if (action === 'remove_user') {
      const updatedUsers = users.filter(user => user.id !== userId);
      setUsers(updatedUsers);
      onUserUpdate?.(updatedUsers);
    } else if (action === 'invite_to_bryzos') {
      // Handle invite logic here
      console.log('Inviting user to Bryzos:', userId);
      const updatedUsers = users.map(user => 
        user.id === userId 
          ? { ...user, status: 'Invited' }
          : user
      );
      setUsers(updatedUsers);
      onUserUpdate?.(updatedUsers);
    }
  };

  const renderEditableCell = (user: User, field: 'userName' | 'email') => {
    const isEditing = editingCell?.userId === user.id && editingCell.field === field;
    const value = user[field];

    if (isEditing) {
      return (
        <input
          type="text"
          value={tempValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onBlur={handleInputBlur}
          onKeyDown={handleInputKeyPress}
          className={styles.editInput}
          autoFocus
        />
      );
    }

    return (
      <span
        className={styles.editableCell}
        onClick={() => handleCellClick(user.id, field, value)}
      >
        {value}
      </span>
    );
  };

  return (
    <div className={styles.tableContainer}>
      <table className={styles.userTable}>
        <thead>
          <tr className={styles.tableHeader}>
            <th>USER NAME</th>
            <th>EMAIL</th>
            <th>LICENSE</th>
            <th>ACTION</th>
            <th>STATUS</th>
          </tr>
        </thead>
        <tbody>
          {users.map((user) => (
            <tr key={user.id} className={styles.tableRow}>
              <td className={styles.tableCell}>
                {renderEditableCell(user, 'userName')}
              </td>
              <td className={styles.tableCell}>
                {renderEditableCell(user, 'email')}
              </td>
              <td className={styles.tableCell}>
                <select
                  value={user.license}
                  onChange={(e) => handleLicenseChange(user.id, e.target.value as 'Assigned' | 'Unassigned')}
                  className={`${styles.dropdown} ${user.license === 'Assigned' ? styles.assigned : styles.unassigned}`}
                >
                  <option value="Assigned">Assigned</option>
                  <option value="Unassigned">Unassigned</option>
                </select>
              </td>
              <td className={styles.tableCell}>
                <select
                  onChange={(e) => {
                    if (e.target.value !== 'Select Action') {
                      handleActionChange(user.id, e.target.value);
                      e.target.value = 'Select Action'; // Reset dropdown
                    }
                  }}
                  className={styles.dropdown}
                  defaultValue="Select Action"
                >
                  <option value="Select Action">Select Action</option>
                  <option value="invite_to_bryzos">Invite to Bryzos</option>
                  <option value="remove_user">Remove User from List</option>
                </select>
              </td>
              <td className={styles.tableCell}>
                <span className={styles.status}>
                  {user.status}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default SubscribeUserTable; 