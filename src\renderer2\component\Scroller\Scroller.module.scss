.scroller {
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  
  &:hover .thumb {
    opacity: 0.8;
  }
}

.hideScrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
  }
}

.track {
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.15) !important;
  }
}

.thumb {
  transition: background-color 0.2s ease, opacity 0.2s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.8) !important;
  }
  
  &:active {
    background-color: rgba(0, 0, 0, 0.9) !important;
  }
}

// Optional theme variants
.scrollerLight {
  .track {
    background-color: rgba(0, 0, 0, 0.05) !important;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.1) !important;
    }
  }
  
  .thumb {
    background-color: rgba(0, 0, 0, 0.4) !important;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.6) !important;
    }
    
    &:active {
      background-color: rgba(0, 0, 0, 0.8) !important;
    }
  }
}

.scrollerDark {
  .track {
    background-color: rgba(255, 255, 255, 0.1) !important;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.15) !important;
    }
  }
  
  .thumb {
    background-color: rgba(255, 255, 255, 0.6) !important;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.8) !important;
    }
    
    &:active {
      background-color: rgba(255, 255, 255, 0.9) !important;
    }
  }
} 