import { useEffect, useMemo, useState } from "react";
import styles from "./SavedBomLeftPanel.module.scss"
import useGetUsersSavedBom from "src/renderer2/hooks/useGetUsersSavedBom";
import Loader from "src/renderer2/Loader/Loader";
import { dateTimeFormat, formatCurrency, formatCurrencyWithComma, formatToTwoDecimalPlaces, getSocketConnection, useCreatePoStore, useGlobalStore } from "@bryzos/giss-ui-library";
import clsx from "clsx";
import { navigatePage } from "src/renderer2/helper";
import { useLeftPanelStore } from "../LeftPanelStore";
import { routes } from "src/renderer2/common";
import dayjs from "dayjs";

const capitalizeText = (text) => {
  if (!text) return '';
  return text.split(' ').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ');
};

const SavedBomLeftPanel = ({savedBomData}: any) => {
    const { setLeftPanelData } = useLeftPanelStore();
    const [selectedSort, setSelectedSort] = useState(3);
    const { setShowLoader , showLoader }: any = useGlobalStore();
    const {setIsCreatePOModule, setCreatePoDataFromSavedBom, setBomProductMappingDataFromSavedBom , bomDataIdToRefresh , setBomDataIdToRefresh, bomProductMappingDataFromSavedBom , createPoDataFromSavedBom} = useCreatePoStore();
    
    const sortData = (data: any[], sortBy: number) => {
        const sortedData = [...data];
        switch (sortBy) {
            case 1:
                return sortedData.sort((a, b) => a?.buyer_internal_po?.localeCompare(b?.buyer_internal_po));
            case 2:
                return sortedData.sort((a, b) => b?.material_total - a?.material_total);
            case 3:
                return savedBomData;
            default:
                return sortedData;
        }
    };

    const sortedBomData = useMemo(() => sortData(savedBomData || [], selectedSort), [savedBomData, selectedSort]);

    const handleBomItemClick = (item: any,index: number) => {
        const isCreatePOModule = (item?.review_status === "COMPLETED" || item?.is_draft_po);
        setIsCreatePOModule(isCreatePOModule);
        if(bomProductMappingDataFromSavedBom?.id === item?.id || createPoDataFromSavedBom?.id === item?.id || showLoader){
            return;
        }
        if(bomDataIdToRefresh){
            const socket = getSocketConnection();
            socket?.emit('getBomDataById', { bom_upload_id: bomDataIdToRefresh });
        }
        setShowLoader(true)
        setCreatePoDataFromSavedBom(null);
        setBomProductMappingDataFromSavedBom(null);
        if(isCreatePOModule){
            const formattedCreatePoData = {
                delivery_date: dayjs(item?.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit),
                shipping_details: {
                    line1: item?.line1,
                    line2: item?.line2,
                    city: item?.city,
                    state_id: item?.state_id,
                    zip: item?.zip,
                },
                order_type: item?.type,
                internal_po_number: item?.buyer_internal_po, 
                bom_id: item?.id,
            }           
            setCreatePoDataFromSavedBom({...formattedCreatePoData, ...item});
        }
        else{
            const formattedCreatePoData = {
                delivery_date: dayjs(item?.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit),
                shipping_details: {
                    line1: item?.line1,
                    line2: item?.line2,
                    city: item?.city,
                    state_id: item?.state_id,
                    zip: item?.zip,
                },
                order_type: item?.type,
                internal_po_number: item?.buyer_internal_po, 
                bom_id: item?.id,
            }   
            setBomProductMappingDataFromSavedBom({...item, ...formattedCreatePoData});
        }
        setBomDataIdToRefresh(null)
    }
    return (
        <div className={styles.savedBomLeftPanelContainer}>
                <>
                    <div className={styles.actionBtns}>
                        <button className={clsx(styles.actionBtn, selectedSort === 1 && styles.active)} onClick={() => setSelectedSort(1)} tabIndex={9}>A to Z</button>
                        <button className={clsx(styles.actionBtn, selectedSort === 2 && styles.active)} onClick={() => setSelectedSort(2)} tabIndex={9}>$  to  $</button>
                        <button className={clsx(styles.actionBtn, selectedSort === 3 && styles.active)} onClick={() => setSelectedSort(3)} tabIndex={9}>New to Old</button>
                    </div>
                    <div className={styles.savedBomList}>
                        {
                            sortedBomData?.length > 0 ? sortedBomData?.map((item: any,index: number) => (
                                <span className={clsx(styles.savedBomItem, (bomProductMappingDataFromSavedBom?.id === item?.id || createPoDataFromSavedBom?.id === item?.id ) && styles.active, (showLoader && !(bomProductMappingDataFromSavedBom?.id === item?.id || createPoDataFromSavedBom?.id === item?.id ) ) && styles.disabled)} key={index} onClick={() => handleBomItemClick(item,index)} tabIndex={9} onKeyDown={(e) => {if(e.key === 'Enter'){handleBomItemClick(item,index)}}}>
                                    <div className={styles.savedBomTitle}>
                                        <span className={styles.savedBomJobTitle}>{item?.buyer_internal_po ?? ''}</span>
                                        <span className={clsx(styles.savedBomTotalPrice, item?.review_status != "COMPLETED" && styles.pendingReview)}>${formatToTwoDecimalPlaces(item?.material_total)}</span>
                                    </div>
                                    <div className={styles.savedBomAddress}>
                                        <span className={styles.savedBomAddressDetails}><span className={styles.savedBomDetails}>{item?.line1 ?? ''} {item?.city ?? ''}</span>{(item?.review_status != "COMPLETED" || item?.is_draft_po) && <span className={styles.pendingReviewStatus}>{capitalizeText(item?.is_draft_po ? 'DRAFT' : item?.review_status)}</span>}</span>    
                                        <span className={styles.savedBomDetails}>{item?.delivery_date ? 'Deliver by ' + dayjs(item?.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit) : ''}</span>
                                        <span className={styles.savedBomDetails}>
                                            {
                                                Array.from(new Set(
                                                    item?.items
                                                        .filter((obj: any) => (obj.status === "APPROVED" || item?.is_draft_po))
                                                        .map((obj: any) => obj?.shape ?? '')
                                                )).join(', ')
                                            }
                                        </span>
                                    </div>
                                </span>
                            )) 
                            : 
                            <div className={styles.noDataContainer}>
                                <div className={styles.noData}>
                                    <span>NO SAVED BOMS <br /> AVAILABLE YET</span>
                                </div>
                                <button onClick={() => navigatePage(location.pathname, { path: routes.createPoPage })}></button>
                            </div>
                        }
                    </div>
                </>
        </div>
    )
}

export default SavedBomLeftPanel
