.custom-column-creator {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 132px;
  flex-grow: 0;
  background-color: #dbdcde;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.custom-col-top-section {
  display: flex;
  column-gap: 12px;
  padding: 10px 12px 8px;
  box-shadow: 0 2px 11.7px 1px #000;
  position: relative;
  z-index: 0;
}

.custome-col-1 {
  display: flex;
  flex-direction: column;
}

.custom-column-header {
  font-family: Syncopate;
  font-size: 12px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: -0.12px;
  text-align: left;
  color: #0f0f14;
  margin-bottom: 8px;
}

/* .custom-column-form {
  margin-bottom: 15px;
} */

.form-group {
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0px;
  }
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 12px;
}

.input-box input {
  width: 147px;
  height: 26px;
  flex-grow: 0;
  padding: 8px 17px 6px 15px;
  border-radius: 5px;
  background-color: #fff;
  border: 0px;
  font-family: Syncopate;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #0f0f14;
}

.dropdown-container {
  position: relative;
}

.custom-dropdown-open {
  border-radius: 5px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.3);
  background-color: #fff;
  height: 130px;
  position: absolute;

}

.custom-dropdown-open .dropdown-header {
  border-radius: 5px 5px 0px 0px;
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 147px;
  height: 26px;
  padding: 8px 9px 6px 15px;
  border-radius: 5px;
  background-color: #fff;
  cursor: pointer;
  font-family: Syncopate;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #0f0f14;
}

.dropdown-arrow {
  font-size: 10px;
}

.dropdown-menu {
  position: absolute;
  top: 26px;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-top: none;
  border-radius: 0 0 5px 5px;
  z-index: 1;
  padding: 0px 8px;
}

.dropdown-item {
  font-family: Syncopate;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #0f0f14;
  cursor: pointer;
  padding: 6px 8px 4px 8px;
  border-radius: 4px;

}

.dropdown-item:hover {
  background-color: #dbdcde;
}

.styling-content {
  display: flex;
  flex-direction: row;
  column-gap: 6px;
  width: 100%;
}

.styling-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 8px 4px 4px 4px;
  border-radius: 2px;
  background-color: #fff;
}

.color-picker-col {
  flex: 0 70px;
  width: 100%;
}

.section-header {
  font-family: Syncopate;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #0f0f14;
  margin-bottom: 4px;
}

.color-picker {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Style for react-colorful */
.react-colorful {
  width: 100% !important;
  height: 80px !important;
  padding: 0 !important;
}

/* Make the saturation area (main color field) slightly taller */
.react-colorful__saturation {
  border-radius: 3px 3px 0 0 !important;
  height: 75px !important;
}

/* Style the hue slider */
.react-colorful__hue {
  height: 8px !important;
  margin-top: 3px !important;
  border-radius: 20px !important;
}

/* Style the alpha/opacity slider */
.react-colorful__alpha {
  height: 8px !important;
  margin-top: 3px !important;
  border-radius: 20px !important;
}

/* Style all sliders' thumbs */
.react-colorful__saturation-pointer,
.react-colorful__hue-pointer,
.react-colorful__alpha-pointer {
  width: 9px !important;
  height: 9px !important;
  border-width: 2px !important;
}

/* Add a checkerboard background for the alpha slider to better visualize transparency */
.react-colorful__alpha {
  background-image: linear-gradient(45deg, #ccc 25%, transparent 25%),
    linear-gradient(-45deg, #ccc 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #ccc 75%),
    linear-gradient(-45deg, transparent 75%, #ccc 75%);
  background-size: 10px 10px;
  background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  gap: 3px;
}

.preview-field {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-header {
  font-family: Teko;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #fff;
  width: 100%;
  text-align: center;
  height: 12px;
  border-radius: 3px 3px 0 0;
  display: block;
}

.preview-box {
  border: 1px solid #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: normal;
  background-color: #e6f2ff;
  margin-top: 0;
  border-radius: 0 0 3px 3px;
}

.small-header {
  width: 18px;
}

.preview-box.small {
  width: 18px;
  height: 68px;
}

.preview-box.medium {
  width: 24px;
  height: 68px;
}

.preview-box.large {
  width: 48px;
  height: 68px;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 56px;
  padding: 12px 24px 12px 24px;
  box-shadow: 0 2px 11.7px 1px #000;
  background-color: #dbdcde;
  position: relative;
  z-index: -1;
}

.cancel-button,
.save-button {
  padding: 0px;
  border: none;
  cursor: pointer;
  background-color: transparent;
}

.cancel-button {
  font-family: Syncopate;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: -0.6px;
  text-align: center;
  color: #0f0f14;
}

.save-button {
  width: 120px;
  height: 22px;
  flex-grow: 0;
  margin: 0 0 0 303px;
  padding: 6px 39px 4px 40px;
  border-radius: 8px;
  background-color: #9b9eac;
  font-family: Syncopate;
  font-size: 12px;
  font-weight: bold;
  line-height: normal;
  letter-spacing: -0.6px;
  text-align: center;
  color: rgba(15, 15, 20, 0.5);
}

/* .cancel-button:hover {
  background-color: #e0e0e0;
}

.save-button:hover {
  background-color: #0069d9;
} */

@media (max-width: 850px) {
  .custom-column-creator {
    width: 95%;
    max-width: 700px;
  }

  .styling-content {
    flex-wrap: wrap;
  }

  .styling-section {
    margin: 10px;
    min-width: 150px;
  }
}

@media (max-width: 600px) {
  .styling-content {
    flex-direction: column;
  }

  .styling-section {
    max-width: 100%;
    width: 100%;
    margin: 10px 0;
  }
}

.dataTypeDropdown {
  width: 147px;
  height: 26px;

  .MuiSelect-select {
    padding: 4px 9px 4px 15px;
    border-radius: 5px;
    background-color: #fff;
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 16px;
    letter-spacing: normal;
    text-align: left;
    color: #0f0f14;
    display: block;
    text-transform: uppercase;

  }

  input {
    &:focus {
      outline: none;
    }
  }

  svg {
    transform: unset;

    path {
      fill: #0f0f14
    }
  }

  fieldset {
    border: none;
    border-color: transparent;
  }
}

.custom-dropdown-open {
  .dataTypeDropdown {
    .MuiSelect-select {
      color: #0f0f14;
    }
  }
}

.dropdownPaper.dropdownPaper {
  box-shadow: none;
  padding: 0px;

  ul {
    padding: 2px 5px;

    li {
      font-family: Syncopate;
      font-size: 12px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: normal;
      text-align: left;
      color: #0f0f14;
      padding: 6px 8px 4px 8px;

      &[aria-selected="true"]{
        background-color: transparent;
      }

      &:hover {
        border-radius: 4px;
        background-color: #dbdcde;
      }
    }
  }
}

.ag-header-viewport {
  width: 100%;
  background-color: #252529;
}