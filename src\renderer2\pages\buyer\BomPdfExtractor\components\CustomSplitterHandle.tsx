import React from 'react';
import { ReactComponent as SplitIcon } from '../../../../assets/New-images/New-Image-latest/BOM-Extractor/split-icon.svg';
import styles from '../styles/BomExtractor.module.scss';
import clsx from 'clsx';

interface CustomSplitterHandleProps {
  isDragging?: boolean;
}

const CustomSplitterHandle: React.FC<CustomSplitterHandleProps> = ({ isDragging = false }) => {
  return (
    <div className={clsx(styles.customSplitterHandle, isDragging && styles.dragging)}>
      <SplitIcon 
        className={clsx(
          styles.splitIcon,
          isDragging && styles.dragging
        )}
      />
    </div>
  );
};

export default CustomSplitterHandle; 