import { useEffect, useState } from 'react';
import styles from './MyChats.module.scss'
import Chat<PERSON>ithVendor from './ChatWithVendor';

import clsx from 'clsx';
import { useChatWithVendorStore } from '@bryzos/giss-ui-library';

const MyChats = () => {
    const [isChatWithVendor, setIsChatWithVendor] = useState(false);
    const {allChatPOs, setChannelName, setPoNumber, setCompanyName} = useChatWithVendorStore();
    
    const handleOpenChat = (chatDetails: any) => {
        setChannelName(chatDetails.reference_id);
        setPoNumber(chatDetails.reference_id);
        setCompanyName('');

        setIsChatWithVendor(true);
    }

    const closeChatWithVendor = () => {
        setIsChatWithVendor(false);
    }

    return(
        <>
            <div className={styles.claimOrderRightWindow}>
                <div className={styles.claimOrderRightWindowHeader}>
                <div className={styles.btnSection}>
                    <h3>My Chats</h3>
                    <div style={{overflowY:'auto'}}><div style={{maxHeight:200}}>
                        {
                            allChatPOs?.length > 0 ? allChatPOs.map((item: any, index: number) => (
                                <button key={index} onClick={() => handleOpenChat(item)}>
                                    {item.reference_id}
                                </button>
                            )):
                            <div style={{color:'#fff'}}>
                                <span>NO CHATS <br /> AVAILABLE YET</span>
                            </div>
                        }
                    </div></div>
                </div>
                </div>
            </div>
            {isChatWithVendor && (
                <div className={clsx(styles.claimOrderRightWindow,styles.chatWithVendor)}>
                    <ChatWithVendor 
                        close={closeChatWithVendor}
                    />
                </div>
            )}
        </>
    )
}
export default MyChats
