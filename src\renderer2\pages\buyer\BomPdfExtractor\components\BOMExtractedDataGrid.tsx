import React, { useEffect, useRef, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import type { ColDef } from "ag-grid-community";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community'; // Import all modules
import { useBomPdfExtractorStore } from "../BomPdfExtractorStore";
import styles from "../styles/BomExtractor.module.scss";
import { useGlobalStore } from "@bryzos/giss-ui-library";
import { parseSteel } from '@bryzos/steel-search-lib';
import { RowData } from "src/renderer2/pages/exporttToExcelUtils";
import { exportToExcel } from "src/renderer2/helper";

const INITIAL_COLUMNS: ColDef[] = [];

const BOMExtractedDataGrid: React.FC = () => {
  const gridRef = useRef<AgGridReact>(null);
  const emptyTableRef = useRef<HTMLDivElement>(null);
  const [gridColumnDefs, setGridColumnDefs] = useState<ColDef[]>(INITIAL_COLUMNS);
  const [rowData, setRowData] = useState<any[]>([]);
  const [emptyRowCount, setEmptyRowCount] = useState<number>(18);
  const {columnDef, gridRowData, productSearcher, setFinalExtractedData, setDoSubmit} = useBomPdfExtractorStore();
  const {productData} = useGlobalStore();
  
  // Calculate number of empty rows based on container height
  useEffect(() => {
    const calculateEmptyRows = () => {
      if (emptyTableRef.current && rowData.length === 0) {
        const containerHeight = emptyTableRef.current.clientHeight;
        const headerHeight = 61; // Approximate header height
        const rowHeight = 40; // Approximate row height based on CSS
        const padding = 40; // Container padding

        const availableHeight = containerHeight - headerHeight - padding;
        const calculatedRows = Math.floor(availableHeight / rowHeight);

        // Ensure minimum of 8 rows and maximum of 20 rows
        const finalRowCount = Math.max(8, Math.min(calculatedRows, 20));
        setEmptyRowCount(finalRowCount);
      }
    };

    calculateEmptyRows();

    // Recalculate on window resize
    const handleResize = () => calculateEmptyRows();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, [rowData.length]);

  useEffect(()=>{
    setGridColumnDefs(prev=>columnDef.map(item=>({...item, onCellValueChanged: onCellValueChanged })));
  },[columnDef]);

  const onCellValueChanged = (params)=>{
    const searchCriteria = [
      params.data.description || '',
      params.data.length || '',
      params.data.specification || '',
      params.data.grade || ''
    ].filter(Boolean).join(' ');

    const splitObj = parseSteel(searchCriteria);
    const splitString = [
      splitObj.shape || '',
      splitObj.dims || '',
      splitObj.length || '',
      splitObj.grade || ''
    ].filter(Boolean).join(' ');
    if(!splitObj.shape && !splitObj.dims && !splitObj.length && !splitObj.grade){
      // bad match set the productIds to empty array
      params.data.productIds = [];
      params.data.ProductDescription = '';
      params.api.refreshCells({ rowNodes: [params.node], columns: ['ProductDescription'] });
      return;
    }
    const newResult = productSearcher.search(splitObj, true);
    if(newResult?.results.length === 1){
      params.data.productIds = [newResult.results[0].Product_ID];
      params.data.ProductDescription = getProductDescription(newResult.results[0].Product_ID);
      params.api.refreshCells({ rowNodes: [params.node], columns: ['ProductDescription'] });
    }else{
      params.data.productIds = [];
      params.data.ProductDescription = '';
      params.api.refreshCells({ rowNodes: [params.node], columns: ['ProductDescription'] });
    }
  }

  const getProductDescription = (id) => {
    if(!productData && !id) return '';
    const product = productData.find(item=>item.Product_ID === id);
    if(product){
      return product.UI_Description;
    }

    return ''
  }

  useEffect(()=>{
    if(gridRowData.length === 0) {
      setRowData([])
      return;
    }
    const tempRowData = gridRowData.map((row)=>{
      return {...row, ProductDescription:row.productIds.length === 1?getProductDescription(row.productIds[0]):''};
    });
    //setGridColumnDefs([...columnDef, { field: "ProductDescription", headerName: "Matched Product" }]);
    setGridColumnDefs((prev)=>{
      const temp = columnDef.map(item=>({...item, onCellValueChanged: onCellValueChanged }))
      return [...temp, { field: "ProductDescription", headerName: "Matched Product" }];
    });
    setRowData([...tempRowData]);
  },[gridRowData]);

  const handleExportToExcel = () => {
    // const rowData: any[] = [];
    // gridRef.current?.api.forEachNode(({data}) => {
    //   const temp = gridColumnDefs.reduce((acc, col) => {
    //       acc[col.field] = data[col.field];
    //       return acc;
    //     }, {})
    //     rowData.push(temp);
    // });
    // const worksheet = XLSX.utils.json_to_sheet(rowData);
    // const workbook = XLSX.utils.book_new();
    // XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
    // XLSX.writeFile(workbook, "grid-data.xlsx");

    const rowData: RowData[] = [];
    const headerRow: RowData = {
      row:[],
      style: { b: true, backgroundColor: 'FFFFEB9C', border:{
        top: { style: 'thin' },
        left: { style: 'thin' },
        right: { style: 'thin' },
        bottom: { style: 'thick' },
      }}
    };

    gridColumnDefs.forEach(item=>{
      headerRow.row.push({ cellText: item.headerName}); 
    });

    rowData.push(headerRow);

    gridRef.current?.api.forEachNode(({data}) => {
      const temp = gridColumnDefs.reduce((acc, col) => {
          acc.push({ cellText: data[col.field]});
          return acc;
        }, [])
        rowData.push({
          row: temp, style: { border: true}
        });
    });

    exportToExcel(rowData, 'BOMExtractedData');

  };

  const getPricingPage = () => {
    setFinalExtractedData(prev=>rowData.map(item=>({...item})));
    setDoSubmit(true);
  };


  return (
    <div style={{width:'100%', height:'100%',position:'relative'}}>
      <div className={styles.agGridHeader}><span className={styles.noteText}>Edit extracted data as needed in each cell.</span><button className={styles.btnExport} onClick={handleExportToExcel}>Export</button></div>
      <div className="ag-theme-alpine" style={{ height: "100%", width: "100%" }}>
        {rowData.length > 0 ? (
          <AgGridReact
            ref={gridRef}
            columnDefs={gridColumnDefs}
            rowData={rowData}
            headerHeight={61}
            rowHeight={40}
            getRowStyle={(params) => {
              return {
                backgroundColor: params.node.rowIndex % 2 === 0 ? '#ffffff' : '#f2f4f6',
              };
            }}
          />
        ) : (
          <div ref={emptyTableRef} className={styles.emptyTableContainer} style={{height: '100%'}}>
            <table className={styles.emptyTable}>
              <thead>
                <tr>
                  <th className={styles.emptyTableHeader}></th>
                  <th className={styles.emptyTableHeader}></th>
                  <th className={styles.emptyTableHeader}></th>
                  <th className={styles.emptyTableHeader}></th>
                  <th className={styles.emptyTableHeader}></th>
                </tr>
              </thead>
              <tbody>
                {[...Array(emptyRowCount)].map((_, index) => (
                  <tr key={index} className={styles.emptyTableRow}>
                    <td className={styles.emptyTableCell}></td>
                    <td className={styles.emptyTableCell}></td>
                    <td className={styles.emptyTableCell}></td>
                    <td className={styles.emptyTableCell}></td>
                    <td className={styles.emptyTableCell}></td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      <button disabled={rowData.length === 0} className={styles.btnGetPricing} onClick={getPricingPage}>GET PRICING</button>
    </div>
  );
};

export default BOMExtractedDataGrid;
