.container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  max-width: 607.8px;
}

.inputContainer {
  width: 100%;

  .input {
    width: 100%;
    height: 40px;
    padding: 7px 12px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 0.56px;
    text-align: left;
    color: #fff;
    border: 0px;
    transition: border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;

    &::placeholder {
      color: #616575;
    }

    &:focus {
      outline: none;
      // border-color: #459fff;
      // background-color: #3e3e44;
    }
  }
  &.multiInputContainer{
    input{
      height: 30px;
    }
  }
}

.inputFocused {
  border-color: #459fff;
  background-color: #3e3e44;
}

.inputError {
  border-color: #ff4444;
}

.inputWithSelectedStates {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.selectedStatesContainer {
  background-color: #2a2a2e;
  border: 1px solid #3e3e44;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  padding: 8px 12px;
  min-height: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.selectedStateTag {
  background-color: #459fff;
  color: #ffffff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-family: Syncopate;
  display: inline-block;
}

.statesGrid {
  display: grid;
  grid-template-columns: repeat(17, 1fr);
  gap: 2px;
  padding: 4px;
  -webkit-backdrop-filter: blur(22.4px);
  backdrop-filter: blur(22.4px);
  box-shadow: 0 4px 12.1px 0 #000;
  background-color: rgb(113, 115, 127);
  width: 100%;
  &.statesGrid1{
    background-color: rgb(128 130 140);
  }
}

.stateItem {
  width: 33.2px;
  height: 24.1px;
  padding: 2px 2px;
  border-radius: 2.4px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  font-family: Syncopate;
  font-size: 12.1px;
  font-weight: normal;
  font-stretch: normal;

  font-style: normal;
  line-height: normal;
  letter-spacing: 0.48px;
  text-align: left;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
  border: 1px solid transparent;
  background: transparent;

  &:focus {
    background-color: #3e3e44;
  }

  &:hover {
    background-color: #3e3e44;
  }
}

// Keyboard navigation hover effect
.hovered {
  background-color: #459fff !important;
  color: #ffffff !important;
  border: 1px solid #459fff;
}

// Default state - grey
.default {
  color: rgba(255, 255, 255, 0.3);

}

// Selected state - blue background
.selected {
  background-color: #459fff !important;
  color: #ffffff !important;

  &:hover {
    background-color: #459fff;
  }
}

// Exact match - blue text
.exactMatch {
  color: #459fff;
  font-weight: 600;
}

// Starts with match - white text
.startsWithMatch {
  color: #ffffff;
}

// No match - grey text but still visible
.noMatch {
  color: #888888;
  opacity: 0.6;
}


.addressInputsMain {
  position: relative;
  background-size: cover;
  background-position: center;

  &.selectShade {
    height: 36px;
    top: 0px;
    position: relative;
    border-radius: 0px 0px 8px 8px;
  }

  .shape1 {
    width: 13px;
    height: 14px;
    top: 22px;
    left: -8px;
    position: absolute;
    z-index: 1;
  }

  .shape2 {
    width: 13px;
    height: 14px;
    position: absolute;
    top: 30px;
    right: -16px;
    transform: rotate(86deg);
    z-index: 1;
  }
}


.popperPaper {
  background-color: transparent !important;
  box-shadow: none !important;
  margin-top: 4px !important;
}

.popperPaperMulti {
  background-color: transparent !important;
  box-shadow: none !important;
  margin-top: -6px !important;
}

.stateWrapper.stateWrapper {
  height: 39px;
  border-radius: 8px 8px 0px 0px;
  -webkit-backdrop-filter: blur(22.4px);
  backdrop-filter: blur(22.4px);
  // box-shadow: 0 4px 12.1px 0 #000;
  background-color: rgb(113, 115, 127);
  display: flex;
  align-items: center;
  padding: 1px 0px 0px 5px;

  svg {
    position: absolute;
    right: 0px;
    top: 9px;
  }

  input {
    width: 56px;
    height: 29.3px;
    padding: 5px 2.4px 4px 4.5px;
    border-radius: 6px;
    background-color: #111217;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: left;
    color: #fff;

    &::placeholder {
      color: rgba(97, 101, 117, 0.5);
    }
  }
}

.MultiStateSelectDropdown {
  position: relative;
  background-size: cover;
  background-position: center;

  &.selectShade {
    height: 36px;
    top: 0px;
    margin-top: -6px;
    position: relative;
    border-radius: 0px 0px 8px 8px;
  }

  .shape1 {
    width: 13px;
    height: 14px;
    top: -7px;
    left: -7px;
    position: absolute;
    z-index: 1;
  }

  .shape2 {
    width: 13px;
    height: 14px;
    position: absolute;
    top: -6px;
    right: -6px;
    transform: rotate(272deg);
    z-index: 1;
  }

  .stateWrapper.stateWrapper {
    height: 35px;
    border-radius: 0px 0px 8px 8px;
    -webkit-backdrop-filter: blur(22.4px);
    backdrop-filter: blur(22.4px);
    // box-shadow: 0 4px 12.1px 0 #000;
    background-color: rgb(128 130 140);
    display: flex;
    align-items: center;
    padding: 1px 0px 0px 4px;

    svg {
      position: absolute;
      right: 2px;
      top: 9px;
    }

    input {
      width: 88%;
      height: 24px;
      padding: 6px 10.3px 3px 5px;
      border-radius: 6px;
      background-color: #111217;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: 0.56px;
      text-align: left;
      color: #fff;

      &::placeholder {
        color: rgba(97, 101, 117, 0.5);
      }
    }
  }
}