<svg width="58" height="59" viewBox="0 0 58 59" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_di_64_867)">
<circle cx="29" cy="25" r="25" fill="#1A1A20"/>
<circle cx="29" cy="25" r="24.5" stroke="url(#paint0_linear_64_867)"/>
</g>
<path d="M23.75 33.2501C23.5582 33.2501 23.3662 33.1767 23.2198 33.0303L15.7198 25.5303C15.4267 25.2372 15.4267 24.7627 15.7198 24.4698L23.2198 16.9698C23.5128 16.6767 23.9874 16.6767 24.2803 16.9698C24.5732 17.2629 24.5733 17.7374 24.2803 18.0303L17.3105 25L24.2803 31.9698C24.5733 32.2629 24.5733 32.7374 24.2803 33.0303C24.1338 33.1767 23.9418 33.2501 23.75 33.2501Z" fill="#A0A0A0"/>
<path d="M33.25 16.7499C33.4418 16.7499 33.6338 16.8233 33.7802 16.9697L41.2802 24.4697C41.5733 24.7628 41.5733 25.2373 41.2802 25.5302L33.7802 33.0302C33.4872 33.3233 33.0126 33.3233 32.7197 33.0302C32.4268 32.7371 32.4267 32.2626 32.7197 31.9697L39.6895 25L32.7197 18.0302C32.4267 17.7371 32.4267 17.2626 32.7197 16.9697C32.8662 16.8233 33.0582 16.7499 33.25 16.7499Z" fill="#A0A0A0"/>
<defs>
<filter id="filter0_di_64_867" x="0" y="-2" width="58" height="61" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_867"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_867" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="6.45"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.37 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_64_867"/>
</filter>
<linearGradient id="paint0_linear_64_867" x1="29" y1="0" x2="29" y2="50" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1D1E24"/>
</linearGradient>
</defs>
</svg>
