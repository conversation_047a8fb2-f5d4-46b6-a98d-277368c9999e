import * as yup from 'yup';

const isEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export const userSchema = yup.object().shape({
  // Placeholder for User tab fields
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  email: yup.string().email('Invalid email format').required('Email is required') .test('is-email', 'Invalid email format', function(value) {
    if(!value) return true;
    return isEmail(value);
  }),  
  phoneNumber: yup.string().min(12, 'Phone number is not valid').required('Phone number is required'),
  searchZipcode: yup.string().min(5, 'Zipcode is not valid').required('Zipcode is required'),
  stateSubscription: yup.array(),
});

export type UserFormData = yup.InferType<typeof userSchema>;
