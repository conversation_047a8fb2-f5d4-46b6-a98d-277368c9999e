import React, { useEffect } from 'react'
import styles from './SubscribeTab.module.scss'
import clsx from 'clsx'
import NextPaymentDetail from './NextPaymentDetail'
import SubscribeUserTable from './SubscribeUserTable'
import { useAppStore } from 'src/renderer2/store/AppStore'

const SubscribeTab = ({setActiveTab, setSaveFunctions}: {setActiveTab: (tab: string) => void, setSaveFunctions: (saveFunctions: any) => void}) => {
    const setMainWindowWidth = useAppStore(state => state.setMainWindowWidth);

    useEffect(() => {
        setMainWindowWidth(74.33)
        return () => {
            setMainWindowWidth(null)
        }
    }, [])

    return (
        <div className={styles.subscribeTab}>
            <div className={clsx(styles.subscribeTabHeader, styles.grid)}>
                <div className={clsx(styles.nextPaymentDetail, styles.equalSpacing)}>
                    <NextPaymentDetail />
                </div>
                <div className={clsx(styles.licenseDetail, styles.equalSpacing)}>
                    <span className={styles.licenseDetailText}>1 OF 1 LICENSES ASSIGNED</span>
                </div>
                {/* <h1>Subscription</h1>
                <div className={styles.subscribeTabHeaderRight}>
                    <button className={styles.subscribeTabHeaderRightButton}>
                        <span>Save</span>
                    </button>
                </div> */}
            </div>
            <div className={clsx(styles.subscriptionActions, styles.grid)}>
                <div className={clsx(styles.actionbarTitle, styles.equalSpacing)}>
                    <p className={styles.title}>ADD USERS & ASSIGN LICENSES</p>
                    <p className={styles.description}>After you purchase licenses, you can assign those licenses to users.</p>
                </div>
                <div className={clsx(styles.actionbarButtons, styles.equalSpacing)}>
                    <button className={styles.actionbarButton}>
                        <span>Buy More Licenses</span>
                    </button>
                    <button className={styles.actionbarButton}>
                        <span>Edit Payment Info</span>
                    </button>
                    <button className={styles.actionbarButton}>
                        <span>Uplaod User List</span>
                    </button>
                </div>
            </div>
            <div className={styles.subscribeUserTable}>
                    <SubscribeUserTable />
            </div>
        </div>
    )
}

export default SubscribeTab